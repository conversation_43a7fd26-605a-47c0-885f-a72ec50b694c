2025-07-21 11:11:18.673 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 开始导出接口数据: interfaceId=VGOP1-R2.11-24101, dataDate=20250622, revision=1
2025-07-21 11:11:18.675 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101
2025-07-21 11:11:18.675 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20250622
2025-07-21 11:11:18.676 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程，数据库: bms
2025-07-21 11:11:18.676 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse("","0","20250622000000");
2025-07-21 11:11:18.686 [vgop-task-1vgop-task-1] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:298)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:84)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 15 common frames omitted
2025-07-21 11:11:18.687 [vgop-task-1vgop-task-1] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程 bmssp_VGOP_banalyse 执行失败
2025-07-21 11:11:42.184 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 开始导出接口数据: interfaceId=VGOP1-R2.11-24101, dataDate=20250622, revision=1
2025-07-21 11:11:46.408 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101
2025-07-21 11:11:47.846 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20250622
2025-07-21 11:11:47.846 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程，数据库: bms
2025-07-21 11:11:47.846 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse("","0","20250622000000");
2025-07-21 11:11:47.851 [vgop-task-2vgop-task-2] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:298)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:84)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 15 common frames omitted
2025-07-21 11:11:47.852 [vgop-task-2vgop-task-2] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程 bmssp_VGOP_banalyse 执行失败
2025-07-21 11:11:56.696 [vgop-task-3vgop-task-3] INFO  c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20220214, revision=1
2025-07-21 11:12:42.535 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-21 11:12:42.536 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 日统计任务导出目录使用前一天日期: 20220214 -> 20220213
2025-07-21 11:12:42.538 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 创建导出目录: ./VGOPdata/datafile/20220213/daily/, 结果: true
2025-07-21 11:12:42.538 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 临时文件路径: ./VGOPdata/datafile/20220213/daily/a_10000_20220214_VGOP1-R2.10-24205.unl
