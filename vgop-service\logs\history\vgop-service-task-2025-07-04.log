2025-07-04 00:09:20.179 [vgop-scheduler-1] INFO  com.vgop.service.scheduler - Task started: 日常任务调度 (ID: aaecfe82a4bc4b7e, Type: daily)
2025-07-04 00:09:20.221 [vgop-scheduler-1] INFO  c.v.s.scheduler.VgopTaskScheduler - === 开始执行日常任务调度 ===
2025-07-04 00:09:20.222 [vgop-scheduler-1] INFO  c.v.s.scheduler.VgopTaskScheduler - 日常任务数据日期: 20250703
2025-07-04 00:09:22.046 [vgop-scheduler-1] INFO  c.v.s.service.FileTransferService - 开始处理日常数据传输任务，数据日期: 20250703
2025-07-04 00:09:22.046 [vgop-scheduler-1] INFO  c.v.s.service.FileTransferService - 确保数据目录存在: 20250703
2025-07-04 00:09:22.361 [vgop-scheduler-1] INFO  c.v.s.service.FileTransferService - 开始阶段一：数据导出 - 日期: 20250703, 周期: daily
2025-07-04 00:09:22.362 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.10-24202, dataDate=20250703, revision=0
2025-07-04 00:09:22.362 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.10-24201, dataDate=20250703, revision=0
2025-07-04 00:09:22.362 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.10-24203, dataDate=20250703, revision=0
2025-07-04 00:09:22.362 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20250703, revision=0
2025-07-04 00:09:22.362 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.10-24206, dataDate=20250703, revision=0
2025-07-04 00:09:22.363 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20250702000000' and CallBeginTime<'20250703000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-04 00:09:22.363 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250702000000' AND mum.openingtime < '20250703000000' AND mum.phonestate IN ('0','1')
2025-07-04 00:09:22.363 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select '',phonenumber,type,optime,version from ( select phonenumber,1 as type,logintime as optime,version from mcn_apploginlog where logintime>='20250702000000' and logintime<'20250703000000' union all select phonenumber,2 as type,optime,'' as version from mcn_oplog where optime>='20250702000000' and optime<'20250703000000' and opmanner=4 )
2025-07-04 00:09:22.363 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select chargetype,phonenumber,mcnnumber,sendorreceNum,optime from mcn_smslog where optime>='20250702000000' and optime<'20250703000000' and length(phonenumber)='11' and length(sendorreceNum) = 11 and sendorreceNum like '1%'
2025-07-04 00:09:22.363 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select mcnnumber,phonenumber,business,shutdown,mcnimsi,mcnlocationid,numstate,mcnnature,mcnnum,channel, case channel when '9' then '3' else '1' end as mj,openingtime, Optime,mcimsitime,'0',Begintime,Endtime,ServID from mcn_user_minor where openingtime<'20250703000000' and phonenumber!='' and phonenumber is not null and mcnnum in (1,2,3)
2025-07-04 00:09:22.366 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 创建导出目录: ./VGOPdata/datafile/20250703/daily/, 结果: true
2025-07-04 00:09:22.367 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/a_10000_20250703_VGOP1-R2.10-24201.unl
2025-07-04 00:09:22.371 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 创建导出目录: ./VGOPdata/datafile/20250703/daily/, 结果: false
2025-07-04 00:09:22.371 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/i_10000_20250703_VGOP1-R2.10-24202.unl
2025-07-04 00:09:22.373 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 创建导出目录: ./VGOPdata/datafile/20250703/daily/, 结果: false
2025-07-04 00:09:22.373 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/a_10000_20250703_VGOP1-R2.10-24205.unl
2025-07-04 00:09:22.374 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 创建导出目录: ./VGOPdata/datafile/20250703/daily/, 结果: false
2025-07-04 00:09:22.374 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 创建导出目录: ./VGOPdata/datafile/20250703/daily/, 结果: false
2025-07-04 00:09:22.374 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/a_10000_20250703_VGOP1-R2.10-24206.unl
2025-07-04 00:09:22.374 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/a_10000_20250703_VGOP1-R2.10-24203.unl
2025-07-04 00:09:22.376 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.10-24207, dataDate=20250703, revision=0
2025-07-04 00:09:22.377 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from ((select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from mcn_sec_major where openingtime>='20250702000000' and openingtime<'20250703000000') union all (select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from mcn_sec_major2 where openingtime>='20250702000000' and openingtime<'20250703000000'))
2025-07-04 00:09:22.377 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/a_10000_20250703_VGOP1-R2.10-24207.unl
2025-07-04 00:09:22.383 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.11-24101, dataDate=20250703, revision=0
2025-07-04 00:09:22.383 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101
2025-07-04 00:09:22.383 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20250703
2025-07-04 00:09:22.383 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 通过dbaccess执行存储过程，数据库: bms
2025-07-04 00:09:22.383 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse("","0","20250703000000");
2025-07-04 00:09:22.388 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.13-24301, dataDate=20250703, revision=0
2025-07-04 00:09:22.388 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select ServID,ServName from vgop_servtype
2025-07-04 00:09:22.388 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/i_10000_20250703_VGOP1-R2.13-24301.unl
2025-07-04 00:09:22.391 [FileTransfer-Worker] ERROR c.v.s.service.DataExportService - 通过dbaccess执行存储过程时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:1014)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:135)
	at com.vgop.service.service.FileTransferService.lambda$processTransfer$1(FileTransferService.java:138)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 8 common frames omitted
2025-07-04 00:09:22.392 [FileTransfer-Worker] ERROR c.v.s.service.DataExportService - 存储过程 bmssp_VGOP_banalyse 执行失败
2025-07-04 00:09:22.392 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 存储过程 bmssp_VGOP_banalyse 执行完成，继续执行数据导出
2025-07-04 00:09:22.392 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select provinceid,locationid,phonenum,mcnnum,appactivenum,mcnactivenum,paynum,feenum,secphonenum,secmcnnum,amcnnum from bms_vgop_banalyse where stattime='20250702'
2025-07-04 00:09:22.393 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/i_10000_20250703_VGOP1-R2.11-24101.unl
2025-07-04 00:09:22.397 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.13-24302, dataDate=20250703, revision=0
2025-07-04 00:09:22.397 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select channelcode,channelname from vgop_channel
2025-07-04 00:09:22.398 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/i_10000_20250703_VGOP1-R2.13-24302.unl
2025-07-04 00:09:22.403 [FileTransfer-Worker] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.13-24303, dataDate=20250703, revision=0
2025-07-04 00:09:22.403 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select shutdown,shutdownname from vgop_shutdown
2025-07-04 00:09:22.404 [FileTransfer-Worker] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250703/daily/i_10000_20250703_VGOP1-R2.13-24303.unl
2025-07-04 00:09:22.433 [vgop-scheduler-1] ERROR c.v.s.service.FileTransferService - 
========== 数据传输任务结果 ==========
数据日期: 20250703
周期类型: 日常
处理状态: 失败
导出文件数: 0
导出数据行数: 0
上传.dat文件数: 0
上传.verf文件数: 0
处理时间: 386ms
错误信息: 部分接口导出失败: VGOP1-R2.10-24201, VGOP1-R2.10-24202, VGOP1-R2.10-24203, VGOP1-R2.10-24205, VGOP1-R2.10-24206, VGOP1-R2.10-24207, VGOP1-R2.11-24101, VGOP1-R2.13-24301, VGOP1-R2.13-24302, VGOP1-R2.13-24303
失败接口: VGOP1-R2.10-24201, VGOP1-R2.10-24202, VGOP1-R2.10-24203, VGOP1-R2.10-24205, VGOP1-R2.10-24206, VGOP1-R2.10-24207, VGOP1-R2.11-24101, VGOP1-R2.13-24301, VGOP1-R2.13-24302, VGOP1-R2.13-24303
=====================================
2025-07-04 00:09:23.078 [vgop-scheduler-1] INFO  com.vgop.service.scheduler - Task completed: 日常任务调度 (ID: aaecfe82a4bc4b7e, Status: FAILED, Duration: 2905ms)
2025-07-04 00:09:23.078 [vgop-scheduler-1] INFO  c.v.s.scheduler.VgopTaskScheduler - === 日常任务调度执行完成 ===
2025-07-04 00:09:59.788 [vgop-task-1] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.11-24101, dataDate=20250622, revision=1
2025-07-04 00:09:59.789 [vgop-task-1] INFO  c.v.s.service.DataExportService - 检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101
2025-07-04 00:09:59.789 [vgop-task-1] INFO  c.v.s.service.DataExportService - 调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20250622
2025-07-04 00:09:59.789 [vgop-task-1] INFO  c.v.s.service.DataExportService - 通过dbaccess执行存储过程，数据库: bms
2025-07-04 00:09:59.790 [vgop-task-1] INFO  c.v.s.service.DataExportService - 存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse("","0","20250622000000");
2025-07-04 00:09:59.803 [vgop-task-1] ERROR c.v.s.service.DataExportService - 通过dbaccess执行存储过程时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:1014)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:135)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 15 common frames omitted
2025-07-04 00:09:59.803 [vgop-task-1] ERROR c.v.s.service.DataExportService - 存储过程 bmssp_VGOP_banalyse 执行失败
2025-07-04 00:09:59.803 [vgop-task-1] INFO  c.v.s.service.DataExportService - 存储过程 bmssp_VGOP_banalyse 执行完成，继续执行数据导出
2025-07-04 00:10:07.707 [vgop-task-1] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select provinceid,locationid,phonenum,mcnnum,appactivenum,mcnactivenum,paynum,feenum,secphonenum,secmcnnum,amcnnum from bms_vgop_banalyse where stattime='20250621'
2025-07-04 00:10:07.707 [vgop-task-1] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250622/daily/i_10000_20250622_VGOP1-R2.11-24101.unl
2025-07-04 00:17:00.002 [vgop-task-2] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=VGOP1-R2.11-24101, dataDate=20250622, revision=1
2025-07-04 00:17:00.003 [vgop-task-2] INFO  c.v.s.service.DataExportService - 检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101
2025-07-04 00:17:00.003 [vgop-task-2] INFO  c.v.s.service.DataExportService - 调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20250622
2025-07-04 00:17:00.013 [vgop-task-2] INFO  c.v.s.service.DataExportService - 通过dbaccess执行存储过程，数据库: bms
2025-07-04 00:17:00.013 [vgop-task-2] INFO  c.v.s.service.DataExportService - 存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse("","0","20250622000000");
2025-07-04 00:17:00.017 [vgop-task-2] ERROR c.v.s.service.DataExportService - 通过dbaccess执行存储过程时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:1014)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:135)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 15 common frames omitted
2025-07-04 00:17:00.018 [vgop-task-2] ERROR c.v.s.service.DataExportService - 存储过程 bmssp_VGOP_banalyse 执行失败
2025-07-04 00:17:00.018 [vgop-task-2] INFO  c.v.s.service.DataExportService - 存储过程 bmssp_VGOP_banalyse 执行完成，继续执行数据导出
2025-07-04 00:17:00.018 [vgop-task-2] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: select provinceid,locationid,phonenum,mcnnum,appactivenum,mcnactivenum,paynum,feenum,secphonenum,secmcnnum,amcnnum from bms_vgop_banalyse where stattime='20250621'
2025-07-04 00:17:00.018 [vgop-task-2] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250622/daily/i_10000_20250622_VGOP1-R2.11-24101.unl
