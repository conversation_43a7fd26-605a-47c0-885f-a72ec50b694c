2025-07-21 11:11:05.316 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-21 11:11:06.208 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-21 11:11:18.686 [vgop-task-1vgop-task-1] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:298)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:84)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 15 common frames omitted
2025-07-21 11:11:18.687 [vgop-task-1vgop-task-1] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程 bmssp_VGOP_banalyse 执行失败
2025-07-21 11:11:18.687 [vgop-task-1] ERROR c.v.s.service.VgopTaskScheduler [||||] [] - 统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.11-24101, 错误: 数据导出失败: 存储过程执行失败
com.vgop.service.exception.TaskExecutionException: 数据导出失败: 存储过程执行失败
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-21 11:11:47.851 [vgop-task-2vgop-task-2] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:298)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:84)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 15 common frames omitted
2025-07-21 11:11:47.852 [vgop-task-2vgop-task-2] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程 bmssp_VGOP_banalyse 执行失败
2025-07-21 11:11:47.853 [vgop-task-2] ERROR c.v.s.service.VgopTaskScheduler [||||] [] - 统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.11-24101, 错误: 数据导出失败: 存储过程执行失败
com.vgop.service.exception.TaskExecutionException: 数据导出失败: 存储过程执行失败
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-21 11:12:42.543 [vgop-task-3vgop-task-3] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行dbaccess命令时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)
	at com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:157)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 16 common frames omitted
2025-07-21 11:12:42.544 [vgop-task-3vgop-task-3] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - dbaccess命令执行失败
2025-07-21 11:12:42.544 [vgop-task-3] ERROR c.v.s.service.VgopTaskScheduler [||||] [] - 统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.10-24205, 错误: 数据导出失败: UNLOAD执行失败
com.vgop.service.exception.TaskExecutionException: 数据导出失败: UNLOAD执行失败
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
