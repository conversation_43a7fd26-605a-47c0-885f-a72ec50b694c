#!/bin/bash
#
# 脚本功能: 为 Mcn_contralog 表生成并导入测试数据。
# 使用方法: ./generate_mcn_contralog_data.sh <日期YYYYMMDD> <数量> <数据库名>
# 示例:     ./generate_mcn_contralog_data.sh 20250612 1000 bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 3 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <日期YYYYMMDD> <数量> <数据库名>"
    exit 1
fi

TARGET_DATE=$1
DATA_COUNT=$2
DB_NAME=$3
DELIMITER="|"
DATA_FILE=$(mktemp "mcn_contralog_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 的 Mcn_contralog 表生成 '${DATA_COUNT}' 条数据。"
echo "临时数据文件: ${DATA_FILE}"

# --- Helper Functions ---
# 生成11位手机号，以1开头
generate_phone_11_digit() {
    local seq=$1
    local prefix="1$(shuf -i 30-89 -n 1)"  # 13x, 14x, 15x, 16x, 17x, 18x, 19x
    local suffix=$(printf "%08d" $((seq % 100000000)))
    echo "${prefix}${suffix}"
}

# 生成指定日期的随机时间
generate_datetime() {
    local hour=$(printf "%02d" $(($RANDOM % 24)))
    local minute=$(printf "%02d" $(($RANDOM % 60)))
    local second=$(printf "%02d" $(($RANDOM % 60)))
    echo "${TARGET_DATE}${hour}${minute}${second}"
}

# 计算结束时间（确保晚于开始时间）
calculate_end_time() {
    local begin_time=$1
    local duration=$2
    local begin_ts=$(date -d "${begin_time:0:8} ${begin_time:8:2}:${begin_time:10:2}:${begin_time:12:2}" +%s)
    local end_ts=$((begin_ts + duration))
    date -d "@$end_ts" +%Y%m%d%H%M%S
}

# --- 生成数据 ---
echo "正在生成数据..."

for i in $(seq 1 ${DATA_COUNT}); do
    # 核心字段
    callType=$(($RANDOM % 2))  # 0 或 1
    callingPartyNumber=$(generate_phone_11_digit $i)
    calledPartyNumber=$(generate_phone_11_digit $(($i + 100000)))
    mcnnumber=$(generate_phone_11_digit $(($i + 200000)))
    
    # 时间字段
    callBeginTime=$(generate_datetime)
    callDuration=$(shuf -i 1-3600 -n 1)  # 1秒到1小时
    callEndTime=$(calculate_end_time "$callBeginTime" "$callDuration")
    
    # 确保符合查询条件的字段
    # cause != '80 81'，生成其他值
    cause_options=("0" "16" "17" "18" "19" "21" "22" "31" "34" "38" "41" "42" "43" "44" "47" "50" "55" "57" "58" "63" "65" "69" "79" "88" "95" "96" "97" "98" "99" "102" "111" "127")
    cause=${cause_options[$(($RANDOM % ${#cause_options[@]}))]}
    
    # reason != '1'，生成其他值
    reason_options=("0" "2" "3" "4" "5" "6" "7" "8" "9" "10" "11" "12" "13" "14" "15" "16" "17")
    reason=${reason_options[$(($RANDOM % ${#reason_options[@]}))]}
    
    # 其他可能需要的字段（设置为合理默认值）
    chargeType="0"
    roamFlag="0" 
    chargepartyindicat="1"
    business="0"
    shutdown="0"
    
    # 输出数据行：只包含需要的字段
    # 字段顺序：callType, callingPartyNumber, calledPartyNumber, mcnnumber, callBeginTime, callEndTime, callDuration, cause, reason, chargeType, roamFlag, chargepartyindicat, business, shutdown
    echo "${callType}${DELIMITER}${callingPartyNumber}${DELIMITER}${calledPartyNumber}${DELIMITER}${mcnnumber}${DELIMITER}${callBeginTime}${DELIMITER}${callEndTime}${DELIMITER}${callDuration}${DELIMITER}${cause}${DELIMITER}${reason}${DELIMITER}${chargeType}${DELIMITER}${roamFlag}${DELIMITER}${chargepartyindicat}${DELIMITER}${business}${DELIMITER}${shutdown}" >> "${DATA_FILE}"
done

echo "数据生成完毕，共生成 ${DATA_COUNT} 条记录。"

# --- 准备 LOAD 命令 ---
echo "准备将数据导入数据库..."

SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' 
INSERT INTO Mcn_contralog (
    calltype,
    callingpartynumber, 
    calledpartynumber,
    mcnnumber,
    callbegintime,
    callendtime,
    callduration,
    cause,
    reason,
    chargetype,
    roamflag,
    chargepartyindicat,
    business,
    shutdown
);
"

echo "将要执行以下SQL:"
echo "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
    echo "数据成功导入!"
    echo "导入了 ${DATA_COUNT} 条符合查询条件的记录。"
    rm "${DATA_FILE}"
else
    echo "数据导入失败！"
    echo "临时数据文件 '${DATA_FILE}' 已保留，请检查错误信息。"
    exit 1
fi

echo "脚本执行完毕。可以使用以下SQL验证数据："
echo "SELECT callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration FROM Mcn_contralog WHERE CallBeginTime>='${TARGET_DATE}000000' AND CallBeginTime<'${TARGET_DATE}235959' AND Cause != '80 81' AND reason != '1' AND length(callingPartyNumber) = 11 AND length(calledPartyNumber) = 11 AND callingPartyNumber like '1%' AND calledPartyNumber like '1%';" 