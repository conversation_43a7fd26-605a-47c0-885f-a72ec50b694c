2025-07-21 11:11:01.704 [background-preinit] INFO  o.h.validator.internal.util.Version [||||] [] - HV000001: Hibernate Validator 6.2.0.Final
2025-07-21 11:11:01.825 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Starting VgopServiceApplication using Java 1.8.0_452 on galileo with PID 40684 (C:\workspaces\zjh\vgop\vgop-service\target\classes started by galil in C:\workspaces\zjh\vgop\vgop-service)
2025-07-21 11:11:01.826 [restartedMain] DEBUG c.v.service.VgopServiceApplication [||||] [] - Running with Spring Boot v2.5.5, Spring v5.3.19
2025-07-21 11:11:01.826 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - The following profiles are active: dev
2025-07-21 11:11:01.880 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor [||||] [] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-21 11:11:01.880 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor [||||] [] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-21 11:11:02.986 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\DataExportMapper.class]
2025-07-21 11:11:02.987 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\RevisionMapper.class]
2025-07-21 11:11:02.987 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\ValidationAlertsMapper.class]
2025-07-21 11:11:02.987 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\primary\RevisionTimesMapper.class]
2025-07-21 11:11:02.987 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\secondary\TaskExecutionMapper.class]
2025-07-21 11:11:02.988 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface
2025-07-21 11:11:02.991 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface
2025-07-21 11:11:02.991 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface
2025-07-21 11:11:02.991 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface
2025-07-21 11:11:02.991 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface
2025-07-21 11:11:03.185 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [||||] [] - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-21 11:11:03.829 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat initialized with port(s): 8080 (http)
2025-07-21 11:11:03.840 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-21 11:11:03.843 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Starting service [Tomcat]
2025-07-21 11:11:03.843 [restartedMain] INFO  o.a.catalina.core.StandardEngine [||||] [] - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-21 11:11:03.970 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring embedded WebApplicationContext
2025-07-21 11:11:03.970 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext [||||] [] - Root WebApplicationContext: initialization completed in 2089 ms
2025-07-21 11:11:04.160 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - ValidationEngine已初始化，等待规则配置...
2025-07-21 11:11:05.076 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\zjh\vgop\vgop-service\target\classes\mapper\DataExportMapper.xml]'
2025-07-21 11:11:05.092 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\zjh\vgop\vgop-service\target\classes\mapper\RevisionMapper.xml]'
2025-07-21 11:11:05.101 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\zjh\vgop\vgop-service\target\classes\mapper\TaskExecutionMapper.xml]'
2025-07-21 11:11:05.111 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\zjh\vgop\vgop-service\target\classes\mapper\ValidationAlertsMapper.xml]'
2025-07-21 11:11:05.287 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 开始测试数据库连接 ===
2025-07-21 11:11:05.288 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试主数据源连接...
2025-07-21 11:11:05.316 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-21 11:11:05.333 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-1} inited
2025-07-21 11:11:06.207 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 主数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=主数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-21 11:11:06.207 [restartedMain] INFO  com.vgop.service.util.DatabaseUtil [||||] [] - 检测到数据库产品: GBase Server, URL: jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
2025-07-21 11:11:06.207 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 检测到主数据库类型: gbase
2025-07-21 11:11:06.207 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 是否支持UNLOAD命令: true
2025-07-21 11:11:06.208 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试次数据源连接...
2025-07-21 11:11:06.208 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-21 11:11:06.209 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-2} inited
2025-07-21 11:11:07.022 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 次数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=次数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-21 11:11:07.023 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 数据库连接测试完成 ===
2025-07-21 11:11:07.384 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Using default implementation for ThreadExecutor
2025-07-21 11:11:07.403 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl [||||] [] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-21 11:11:07.404 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Quartz Scheduler v.2.3.2 created.
2025-07-21 11:11:07.408 [restartedMain] INFO  org.quartz.simpl.RAMJobStore [||||] [] - RAMJobStore initialized.
2025-07-21 11:11:07.409 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-21 11:11:07.409 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-21 11:11:07.409 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler version: 2.3.2
2025-07-21 11:11:07.410 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@332b8db3
2025-07-21 11:11:07.465 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver [||||] [] - Exposing 15 endpoint(s) beneath base path '/actuator'
2025-07-21 11:11:07.566 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:07.571 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-21 11:11:07.571 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-21 11:11:07.571 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-21 11:11:07.573 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-21 11:11:07.574 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:07.697 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@289eba1a]
2025-07-21 11:11:07.698 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:07.698 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-21 11:11:07.698 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@289eba1a]: database product name is 'GBase Server'
2025-07-21 11:11:07.699 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-21 11:11:07.699 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-21 11:11:07.700 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-21 11:11:07.700 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-21 11:11:07.700 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-21 11:11:07.700 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:08.078 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-21 11:11:08.079 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-21 11:11:08.079 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:08.459 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-21 11:11:08.459 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置主数据源字符编码安全的JdbcTemplate
2025-07-21 11:11:08.460 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:08.461 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-21 11:11:08.461 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-21 11:11:08.461 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-21 11:11:08.461 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-21 11:11:08.461 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:08.530 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@444462b4]
2025-07-21 11:11:08.530 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:08.530 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-21 11:11:08.531 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@444462b4]: database product name is 'GBase Server'
2025-07-21 11:11:08.531 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-21 11:11:08.531 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-21 11:11:08.531 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-21 11:11:08.531 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-21 11:11:08.531 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-21 11:11:08.531 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:09.048 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-21 11:11:09.048 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-21 11:11:09.048 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 11:11:09.492 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-21 11:11:09.492 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置次数据源字符编码安全的JdbcTemplate
2025-07-21 11:11:09.585 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer [||||] [] - LiveReload server is running on port 35729
2025-07-21 11:11:10.066 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-21 11:11:10.136 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat started on port(s): 8080 (http) with context path '/vgop'
2025-07-21 11:11:10.852 [restartedMain] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Starting Quartz Scheduler now
2025-07-21 11:11:10.853 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-21 11:11:10.876 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Started VgopServiceApplication in 9.957 seconds (JVM running for 11.543)
2025-07-21 11:11:10.885 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 开始验证应用配置...
2025-07-21 11:11:10.886 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 配置验证通过
2025-07-21 11:11:10.886 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 开始初始化目录结构...
2025-07-21 11:11:10.886 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 数据导出根目录 - ./VGOPdata/datafile/
2025-07-21 11:11:10.886 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日志根目录 - ./logs/
2025-07-21 11:11:10.886 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 备份根目录 - ./data/backup/
2025-07-21 11:11:10.886 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 告警文件目录 - ./data/alerts/
2025-07-21 11:11:10.887 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250718 - ./VGOPdata/datafile//20250718/day
2025-07-21 11:11:10.887 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250718 - ./VGOPdata/datafile//20250718/month
2025-07-21 11:11:10.887 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250719 - ./VGOPdata/datafile//20250719/day
2025-07-21 11:11:10.887 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250719 - ./VGOPdata/datafile//20250719/month
2025-07-21 11:11:10.921 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 日数据目录 - 20250720 - ./VGOPdata/datafile//20250720/day
2025-07-21 11:11:10.922 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 月数据目录 - 20250720 - ./VGOPdata/datafile//20250720/month
2025-07-21 11:11:10.925 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 日数据目录 - 20250721 - ./VGOPdata/datafile//20250721/day
2025-07-21 11:11:10.927 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 月数据目录 - 20250721 - ./VGOPdata/datafile//20250721/month
2025-07-21 11:11:10.929 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 日数据目录 - 20250722 - ./VGOPdata/datafile//20250722/day
2025-07-21 11:11:10.931 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 月数据目录 - 20250722 - ./VGOPdata/datafile//20250722/month
2025-07-21 11:11:10.933 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 日数据目录 - 20250723 - ./VGOPdata/datafile//20250723/day
2025-07-21 11:11:10.934 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 月数据目录 - 20250723 - ./VGOPdata/datafile//20250723/month
2025-07-21 11:11:10.934 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月度数据目录 - 202507 - ./VGOPdata/datafile//202507
2025-07-21 11:11:10.934 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 目录结构初始化完成
2025-07-21 11:11:10.936 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 开始从配置文件加载校验规则...
2025-07-21 11:11:10.937 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24201 的校验规则...
2025-07-21 11:11:10.938 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.format - FORMAT
2025-07-21 11:11:10.938 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 11:11:10.939 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: FORMAT
2025-07-21 11:11:10.939 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.length - LENGTH
2025-07-21 11:11:10.939 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 11:11:10.939 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: LENGTH
2025-07-21 11:11:10.939 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonestate.enum - ENUM
2025-07-21 11:11:10.940 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonestate, 规则ID=common.phonestate.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.940 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonestate 添加了规则: ENUM
2025-07-21 11:11:10.940 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimsi.length - LENGTH
2025-07-21 11:11:10.940 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimsi, 规则ID=common.phoneimsi.length, 规则名称=长度校验
2025-07-21 11:11:10.940 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimsi 添加了规则: LENGTH
2025-07-21 11:11:10.940 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimei.length - LENGTH
2025-07-21 11:11:10.940 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimei, 规则ID=common.phoneimei.length, 规则名称=长度校验
2025-07-21 11:11:10.941 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimei 添加了规则: LENGTH
2025-07-21 11:11:10.941 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.locationid.length - LENGTH
2025-07-21 11:11:10.941 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=locationid, 规则ID=common.locationid.length, 规则名称=长度校验
2025-07-21 11:11:10.941 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 locationid 添加了规则: LENGTH
2025-07-21 11:11:10.941 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.provinceid.length - LENGTH
2025-07-21 11:11:10.941 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=provinceid, 规则ID=common.provinceid.length, 规则名称=长度校验
2025-07-21 11:11:10.941 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 provinceid 添加了规则: LENGTH
2025-07-21 11:11:10.941 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.openingtime.format - FORMAT
2025-07-21 11:11:10.941 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-21 11:11:10.941 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 openingtime 添加了规则: FORMAT
2025-07-21 11:11:10.941 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.optime.format - FORMAT
2025-07-21 11:11:10.941 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-21 11:11:10.941 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 Optime 添加了规则: FORMAT
2025-07-21 11:11:10.942 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.sex.enum - ENUM
2025-07-21 11:11:10.942 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=sex, 规则ID=common.sex.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.942 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 sex 添加了规则: ENUM
2025-07-21 11:11:10.942 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24201, 字段数量=9
2025-07-21 11:11:10.942 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了 9 个字段的元数据
2025-07-21 11:11:10.942 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [phonestate, Optime, phoneimei, openingtime, locationid, sex, phonenumber, phoneimsi, provinceid] -> VGOP1-R2-10-24201
2025-07-21 11:11:10.942 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了字段到接口的映射关系
2025-07-21 11:11:10.942 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24201 加载了 10 个字段校验规则
2025-07-21 11:11:10.942 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24202 的校验规则...
2025-07-21 11:11:10.942 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.format - FORMAT
2025-07-21 11:11:10.943 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: FORMAT
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.length - LENGTH
2025-07-21 11:11:10.943 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: LENGTH
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.format - FORMAT
2025-07-21 11:11:10.943 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: FORMAT
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.length - LENGTH
2025-07-21 11:11:10.943 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: LENGTH
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.business.enum - ENUM
2025-07-21 11:11:10.943 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=business, 规则ID=common.business.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 business 添加了规则: ENUM
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.shutdown.length - LENGTH
2025-07-21 11:11:10.943 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-21 11:11:10.943 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 shutdown 添加了规则: LENGTH
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.length - LENGTH
2025-07-21 11:11:10.944 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.length, 规则名称=长度校验
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: LENGTH
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.format - FORMAT
2025-07-21 11:11:10.944 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.format, 规则名称=格式校验
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: FORMAT
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnlocationid.length - LENGTH
2025-07-21 11:11:10.944 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnlocationid, 规则ID=common.mcnlocationid.length, 规则名称=长度校验
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnlocationid 添加了规则: LENGTH
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.numstate.length - LENGTH
2025-07-21 11:11:10.944 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=numstate, 规则ID=common.numstate.length, 规则名称=长度校验
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 numstate 添加了规则: LENGTH
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnature.enum - ENUM
2025-07-21 11:11:10.944 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnature, 规则ID=common.mcnnature.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnature 添加了规则: ENUM
2025-07-21 11:11:10.944 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.enum - ENUM
2025-07-21 11:11:10.944 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.945 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: ENUM
2025-07-21 11:11:10.945 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.length - LENGTH
2025-07-21 11:11:10.945 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.length, 规则名称=长度校验
2025-07-21 11:11:10.945 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: LENGTH
2025-07-21 11:11:10.945 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.channel.length - LENGTH
2025-07-21 11:11:10.945 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=channel, 规则ID=common.channel.length, 规则名称=长度校验
2025-07-21 11:11:10.945 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 channel 添加了规则: LENGTH
2025-07-21 11:11:10.945 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mj.enum - ENUM
2025-07-21 11:11:10.945 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mj, 规则ID=common.mj.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.945 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mj 添加了规则: ENUM
2025-07-21 11:11:10.945 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.openingtime.format - FORMAT
2025-07-21 11:11:10.946 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-21 11:11:10.946 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 openingtime 添加了规则: FORMAT
2025-07-21 11:11:10.946 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.optime.format - FORMAT
2025-07-21 11:11:10.946 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-21 11:11:10.946 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Optime 添加了规则: FORMAT
2025-07-21 11:11:10.946 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcimsitime.format - FORMAT
2025-07-21 11:11:10.946 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcimsitime, 规则ID=common.mcimsitime.format, 规则名称=格式校验
2025-07-21 11:11:10.946 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcimsitime 添加了规则: FORMAT
2025-07-21 11:11:10.947 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.usertype.length - LENGTH
2025-07-21 11:11:10.947 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=usertype, 规则ID=common.usertype.length, 规则名称=长度校验
2025-07-21 11:11:10.947 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 usertype 添加了规则: LENGTH
2025-07-21 11:11:10.947 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.begintime.format - FORMAT
2025-07-21 11:11:10.947 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Begintime, 规则ID=common.Begintime.format, 规则名称=格式校验
2025-07-21 11:11:10.947 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Begintime 添加了规则: FORMAT
2025-07-21 11:11:10.947 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.endtime.format - FORMAT
2025-07-21 11:11:10.947 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Endtime, 规则ID=common.Endtime.format, 规则名称=格式校验
2025-07-21 11:11:10.947 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Endtime 添加了规则: FORMAT
2025-07-21 11:11:10.947 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.servid.length - LENGTH
2025-07-21 11:11:10.947 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-21 11:11:10.948 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 ServID 添加了规则: LENGTH
2025-07-21 11:11:10.948 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24202, 字段数量=18
2025-07-21 11:11:10.948 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了 18 个字段的元数据
2025-07-21 11:11:10.948 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [mcnlocationid, Optime, business, mcnimsi, mcimsitime, phonenumber, mcnnum, channel, usertype, numstate, Endtime, openingtime, ServID, mcnnature, Begintime, mcnnumber, mj, shutdown] -> VGOP1-R2-10-24202
2025-07-21 11:11:10.948 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了字段到接口的映射关系
2025-07-21 11:11:10.948 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24202 加载了 22 个字段校验规则
2025-07-21 11:11:10.948 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24203 的校验规则...
2025-07-21 11:11:10.948 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.account_id.length - LENGTH
2025-07-21 11:11:10.948 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=account_id, 规则ID=common.account_id.length, 规则名称=长度校验
2025-07-21 11:11:10.949 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 account_id 添加了规则: LENGTH
2025-07-21 11:11:10.949 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.format - FORMAT
2025-07-21 11:11:10.949 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 11:11:10.949 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: FORMAT
2025-07-21 11:11:10.949 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.length - LENGTH
2025-07-21 11:11:10.949 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 11:11:10.949 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: LENGTH
2025-07-21 11:11:10.949 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.type.enum - ENUM
2025-07-21 11:11:10.949 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=type, 规则ID=common.type.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.949 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 type 添加了规则: ENUM
2025-07-21 11:11:10.950 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.optime.format - FORMAT
2025-07-21 11:11:10.950 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-21 11:11:10.950 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 optime 添加了规则: FORMAT
2025-07-21 11:11:10.950 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.version.length - LENGTH
2025-07-21 11:11:10.950 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=version, 规则ID=common.version.length, 规则名称=长度校验
2025-07-21 11:11:10.950 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 version 添加了规则: LENGTH
2025-07-21 11:11:10.950 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24203, 字段数量=5
2025-07-21 11:11:10.950 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了 5 个字段的元数据
2025-07-21 11:11:10.950 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [account_id, optime, phonenumber, type, version] -> VGOP1-R2-10-24203
2025-07-21 11:11:10.950 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了字段到接口的映射关系
2025-07-21 11:11:10.951 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24203 加载了 6 个字段校验规则
2025-07-21 11:11:10.951 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24205 的校验规则...
2025-07-21 11:11:10.951 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calltype.enum - ENUM
2025-07-21 11:11:10.951 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callType, 规则ID=common.callType.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.951 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callType 添加了规则: ENUM
2025-07-21 11:11:10.951 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.format - FORMAT
2025-07-21 11:11:10.951 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.format, 规则名称=格式校验
2025-07-21 11:11:10.952 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: FORMAT
2025-07-21 11:11:10.952 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.length - LENGTH
2025-07-21 11:11:10.952 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.length, 规则名称=长度校验
2025-07-21 11:11:10.952 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: LENGTH
2025-07-21 11:11:10.952 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.format - FORMAT
2025-07-21 11:11:10.952 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.format, 规则名称=格式校验
2025-07-21 11:11:10.952 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: FORMAT
2025-07-21 11:11:10.953 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.length - LENGTH
2025-07-21 11:11:10.953 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.length, 规则名称=长度校验
2025-07-21 11:11:10.953 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: LENGTH
2025-07-21 11:11:10.953 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.mcnnumber.format - FORMAT
2025-07-21 11:11:10.953 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-21 11:11:10.953 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 mcnnumber 添加了规则: FORMAT
2025-07-21 11:11:10.953 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callbegintime.format - FORMAT
2025-07-21 11:11:10.953 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallBeginTime, 规则ID=common.CallBeginTime.format, 规则名称=格式校验
2025-07-21 11:11:10.954 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallBeginTime 添加了规则: FORMAT
2025-07-21 11:11:10.954 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callendtime.format - FORMAT
2025-07-21 11:11:10.954 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallEndTime, 规则ID=common.CallEndTime.format, 规则名称=格式校验
2025-07-21 11:11:10.954 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallEndTime 添加了规则: FORMAT
2025-07-21 11:11:10.954 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.format - FORMAT
2025-07-21 11:11:10.954 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.format, 规则名称=格式校验
2025-07-21 11:11:10.954 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: FORMAT
2025-07-21 11:11:10.954 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.range - RANGE
2025-07-21 11:11:10.955 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.range, 规则名称=范围校验
2025-07-21 11:11:10.955 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: RANGE
2025-07-21 11:11:10.955 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24205, 字段数量=7
2025-07-21 11:11:10.955 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了 7 个字段的元数据
2025-07-21 11:11:10.955 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [CallEndTime, callingPartyNumber, calledPartyNumber, CallBeginTime, mcnnumber, CallDuration, callType] -> VGOP1-R2-10-24205
2025-07-21 11:11:10.955 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了字段到接口的映射关系
2025-07-21 11:11:10.955 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24205 加载了 10 个字段校验规则
2025-07-21 11:11:10.955 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24206 的校验规则...
2025-07-21 11:11:10.956 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.chargetype.enum - ENUM
2025-07-21 11:11:10.956 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=chargetype, 规则ID=common.chargetype.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.956 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 chargetype 添加了规则: ENUM
2025-07-21 11:11:10.956 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.format - FORMAT
2025-07-21 11:11:10.956 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 11:11:10.956 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: FORMAT
2025-07-21 11:11:10.956 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.length - LENGTH
2025-07-21 11:11:10.956 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 11:11:10.956 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: LENGTH
2025-07-21 11:11:10.956 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.mcnnumber.format - FORMAT
2025-07-21 11:11:10.956 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-21 11:11:10.957 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 mcnnumber 添加了规则: FORMAT
2025-07-21 11:11:10.957 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.format - FORMAT
2025-07-21 11:11:10.957 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.format, 规则名称=格式校验
2025-07-21 11:11:10.957 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: FORMAT
2025-07-21 11:11:10.957 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.length - LENGTH
2025-07-21 11:11:10.957 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.length, 规则名称=长度校验
2025-07-21 11:11:10.957 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: LENGTH
2025-07-21 11:11:10.957 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.optime.format - FORMAT
2025-07-21 11:11:10.957 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-21 11:11:10.957 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 optime 添加了规则: FORMAT
2025-07-21 11:11:10.957 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24206, 字段数量=5
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了 5 个字段的元数据
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [optime, phonenumber, chargetype, mcnnumber, sendorreceNum] -> VGOP1-R2-10-24206
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了字段到接口的映射关系
2025-07-21 11:11:10.958 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24206 加载了 7 个字段校验规则
2025-07-21 11:11:10.958 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24207 的校验规则...
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.format - FORMAT
2025-07-21 11:11:10.958 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: FORMAT
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.length - LENGTH
2025-07-21 11:11:10.958 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: LENGTH
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.format - FORMAT
2025-07-21 11:11:10.958 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: FORMAT
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.length - LENGTH
2025-07-21 11:11:10.958 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: LENGTH
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.format - FORMAT
2025-07-21 11:11:10.958 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.format, 规则名称=格式校验
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: FORMAT
2025-07-21 11:11:10.958 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.length - LENGTH
2025-07-21 11:11:10.958 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.length, 规则名称=长度校验
2025-07-21 11:11:10.959 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: LENGTH
2025-07-21 11:11:10.959 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.businessstate.enum - ENUM
2025-07-21 11:11:10.959 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=businessState, 规则ID=common.businessState.enum, 规则名称=枚举值校验
2025-07-21 11:11:10.959 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 businessState 添加了规则: ENUM
2025-07-21 11:11:10.959 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.numstate.format - FORMAT
2025-07-21 11:11:10.959 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Numstate, 规则ID=common.Numstate.format, 规则名称=格式校验
2025-07-21 11:11:10.959 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Numstate 添加了规则: FORMAT
2025-07-21 11:11:10.959 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.locationid.length - LENGTH
2025-07-21 11:11:10.959 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Locationid, 规则ID=common.Locationid.length, 规则名称=长度校验
2025-07-21 11:11:10.959 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Locationid 添加了规则: LENGTH
2025-07-21 11:11:10.959 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.format - FORMAT
2025-07-21 11:11:10.959 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.format, 规则名称=格式校验
2025-07-21 11:11:10.959 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: FORMAT
2025-07-21 11:11:10.960 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.range - RANGE
2025-07-21 11:11:10.960 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.range, 规则名称=范围校验
2025-07-21 11:11:10.960 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: RANGE
2025-07-21 11:11:10.960 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.openingtime.format - FORMAT
2025-07-21 11:11:10.960 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-21 11:11:10.960 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 openingtime 添加了规则: FORMAT
2025-07-21 11:11:10.960 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24207, 字段数量=8
2025-07-21 11:11:10.960 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了 8 个字段的元数据
2025-07-21 11:11:10.960 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [Numstate, openingtime, mcnImsi, businessState, BossProvinceid, phonenumber, Locationid, mcnnumber] -> VGOP1-R2-10-24207
2025-07-21 11:11:10.960 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了字段到接口的映射关系
2025-07-21 11:11:10.960 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24207 加载了 12 个字段校验规则
2025-07-21 11:11:10.960 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-11-24101 的校验规则...
2025-07-21 11:11:10.960 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.format - FORMAT
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.format, 规则名称=格式校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: FORMAT
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.range - RANGE
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.range, 规则名称=范围校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: RANGE
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.format - FORMAT
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.format, 规则名称=格式校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: FORMAT
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.range - RANGE
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.range, 规则名称=范围校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: RANGE
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.format - FORMAT
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.format, 规则名称=格式校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: FORMAT
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.range - RANGE
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.range, 规则名称=范围校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: RANGE
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.format - FORMAT
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.format, 规则名称=格式校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: FORMAT
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.range - RANGE
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.range, 规则名称=范围校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: RANGE
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.format - FORMAT
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.format, 规则名称=格式校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: FORMAT
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.range - RANGE
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.range, 规则名称=范围校验
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: RANGE
2025-07-21 11:11:10.961 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.format - FORMAT
2025-07-21 11:11:10.961 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.format, 规则名称=格式校验
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: FORMAT
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.range - RANGE
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.range, 规则名称=范围校验
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: RANGE
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.format - FORMAT
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.format, 规则名称=格式校验
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: FORMAT
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.range - RANGE
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.range, 规则名称=范围校验
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: RANGE
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.format - FORMAT
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.format, 规则名称=格式校验
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: FORMAT
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.range - RANGE
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.range, 规则名称=范围校验
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: RANGE
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.format - FORMAT
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.format, 规则名称=格式校验
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: FORMAT
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.range - RANGE
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.range, 规则名称=范围校验
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: RANGE
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-11-24101, 字段数量=9
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了 9 个字段的元数据
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [appactivenum, paynum, mcnnum, secphonenum, phonenum, mcnactivenum, secmcnnum, amcnnum, feenum] -> VGOP1-R2-11-24101
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了字段到接口的映射关系
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-11-24101 加载了 18 个字段校验规则
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24301 的校验规则...
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.format - FORMAT
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.format, 规则名称=格式校验
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: FORMAT
2025-07-21 11:11:10.962 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.length - LENGTH
2025-07-21 11:11:10.962 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.standard_format - LENGTH
2025-07-21 11:11:10.963 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.length - LENGTH
2025-07-21 11:11:10.963 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.length, 规则名称=长度校验
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: LENGTH
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.content - FORMAT
2025-07-21 11:11:10.963 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.format, 规则名称=格式校验
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: FORMAT
2025-07-21 11:11:10.963 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24301, 字段数量=2
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了 2 个字段的元数据
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [ServName, ServID] -> VGOP1-R2-13-24301
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了字段到接口的映射关系
2025-07-21 11:11:10.963 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24301 加载了 5 个字段校验规则
2025-07-21 11:11:10.963 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24302 的校验规则...
2025-07-21 11:11:10.963 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.format - FORMAT
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.format, 规则名称=格式校验
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: FORMAT
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.length - LENGTH
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.length, 规则名称=长度校验
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: LENGTH
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.range - RANGE
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.range, 规则名称=范围校验
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: RANGE
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.length - LENGTH
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.length, 规则名称=长度校验
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: LENGTH
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.content - FORMAT
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.format, 规则名称=格式校验
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: FORMAT
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24302, 字段数量=2
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了 2 个字段的元数据
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [channelname, channelcode] -> VGOP1-R2-13-24302
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了字段到接口的映射关系
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24302 加载了 5 个字段校验规则
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24303 的校验规则...
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.format - FORMAT
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.format, 规则名称=格式校验
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: FORMAT
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.length - LENGTH
2025-07-21 11:11:10.964 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-21 11:11:10.964 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: LENGTH
2025-07-21 11:11:10.965 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.range - RANGE
2025-07-21 11:11:10.965 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.range, 规则名称=范围校验
2025-07-21 11:11:10.965 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: RANGE
2025-07-21 11:11:10.965 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.length - LENGTH
2025-07-21 11:11:10.965 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.length, 规则名称=长度校验
2025-07-21 11:11:10.965 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: LENGTH
2025-07-21 11:11:10.965 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.content - FORMAT
2025-07-21 11:11:10.965 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.format, 规则名称=格式校验
2025-07-21 11:11:10.965 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: FORMAT
2025-07-21 11:11:10.965 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24303, 字段数量=2
2025-07-21 11:11:10.965 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了 2 个字段的元数据
2025-07-21 11:11:10.965 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [shutdownname, shutdown] -> VGOP1-R2-13-24303
2025-07-21 11:11:10.965 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了字段到接口的映射关系
2025-07-21 11:11:10.965 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24303 加载了 5 个字段校验规则
2025-07-21 11:11:10.965 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 校验规则加载完成，共加载 100 个规则
2025-07-21 11:11:18.434 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 11:11:18.434 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet [||||] [] - Initializing Servlet 'dispatcherServlet'
2025-07-21 11:11:18.436 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet [||||] [] - Completed initialization in 2 ms
2025-07-21 11:11:18.650 [http-nio-8080-exec-1] INFO  c.v.s.controller.VgopTaskController [||||] [] - 接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.11-24101
2025-07-21 11:11:18.671 [vgop-task-1] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.11-24101
2025-07-21 11:11:18.672 [vgop-task-1] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行数据导出任务: VGOP1-R2.11-24101
2025-07-21 11:11:18.672 [vgop-task-1] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - VGOP业务分析任务执行顺序 - 步骤1: 即将通过dbaccess调用存储过程 bmssp_VGOP_banalyse
2025-07-21 11:11:18.673 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 开始导出接口数据: interfaceId=VGOP1-R2.11-24101, dataDate=20250622, revision=1
2025-07-21 11:11:18.675 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101
2025-07-21 11:11:18.675 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20250622
2025-07-21 11:11:18.676 [vgop-task-1vgop-task-1] DEBUG com.vgop.service.util.DatabaseUtil [VGOP1-R2.11-24101|20250622|1||] [export] - 从URL中提取的数据库名称: bms
2025-07-21 11:11:18.676 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程，数据库: bms
2025-07-21 11:11:18.676 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse("","0","20250622000000");
2025-07-21 11:11:18.686 [vgop-task-1vgop-task-1] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:298)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:84)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 15 common frames omitted
2025-07-21 11:11:18.687 [vgop-task-1vgop-task-1] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程 bmssp_VGOP_banalyse 执行失败
2025-07-21 11:11:18.687 [vgop-task-1] ERROR c.v.s.service.VgopTaskScheduler [||||] [] - 统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.11-24101, 错误: 数据导出失败: 存储过程执行失败
com.vgop.service.exception.TaskExecutionException: 数据导出失败: 存储过程执行失败
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-21 11:11:28.443 [http-nio-8080-exec-6] INFO  c.v.s.controller.VgopTaskController [||||] [] - 接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.11-24101
2025-07-21 11:11:28.443 [vgop-task-2] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.11-24101
2025-07-21 11:11:28.444 [vgop-task-2] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行数据导出任务: VGOP1-R2.11-24101
2025-07-21 11:11:28.444 [vgop-task-2] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - VGOP业务分析任务执行顺序 - 步骤1: 即将通过dbaccess调用存储过程 bmssp_VGOP_banalyse
2025-07-21 11:11:42.184 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 开始导出接口数据: interfaceId=VGOP1-R2.11-24101, dataDate=20250622, revision=1
2025-07-21 11:11:46.408 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101
2025-07-21 11:11:47.846 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20250622
2025-07-21 11:11:47.846 [vgop-task-2vgop-task-2] DEBUG com.vgop.service.util.DatabaseUtil [VGOP1-R2.11-24101|20250622|1||] [export] - 从URL中提取的数据库名称: bms
2025-07-21 11:11:47.846 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程，数据库: bms
2025-07-21 11:11:47.846 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse("","0","20250622000000");
2025-07-21 11:11:47.851 [vgop-task-2vgop-task-2] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 通过dbaccess执行存储过程时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:298)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:84)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 15 common frames omitted
2025-07-21 11:11:47.852 [vgop-task-2vgop-task-2] ERROR c.v.s.service.DataExportService [VGOP1-R2.11-24101|20250622|1||] [export] - 存储过程 bmssp_VGOP_banalyse 执行失败
2025-07-21 11:11:47.853 [vgop-task-2] ERROR c.v.s.service.VgopTaskScheduler [||||] [] - 统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.11-24101, 错误: 数据导出失败: 存储过程执行失败
com.vgop.service.exception.TaskExecutionException: 数据导出失败: 存储过程执行失败
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-21 11:11:54.682 [http-nio-8080-exec-3] INFO  c.v.s.controller.VgopTaskController [||||] [] - 接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.10-24205
2025-07-21 11:11:54.683 [vgop-task-3] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.10-24205
2025-07-21 11:11:54.683 [vgop-task-3] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行数据导出任务: VGOP1-R2.10-24205
2025-07-21 11:11:56.696 [vgop-task-3vgop-task-3] INFO  c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20220214, revision=1
2025-07-21 11:12:42.535 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-21 11:12:42.536 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 日统计任务导出目录使用前一天日期: 20220214 -> 20220213
2025-07-21 11:12:42.538 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 创建导出目录: ./VGOPdata/datafile/20220213/daily/, 结果: true
2025-07-21 11:12:42.538 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 临时文件路径: ./VGOPdata/datafile/20220213/daily/a_10000_20220214_VGOP1-R2.10-24205.unl
2025-07-21 11:12:42.539 [vgop-task-3vgop-task-3] INFO  c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行UNLOAD命令通过dbaccess: set lock mode to wait 10;
unload to ./VGOPdata/datafile/20220213/daily/a_10000_20220214_VGOP1-R2.10-24205.unl delimiter '|' select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-21 11:12:42.539 [vgop-task-3vgop-task-3] DEBUG com.vgop.service.util.DatabaseUtil [VGOP1-R2.10-24205|20220214|1||] [unload] - 从URL中提取的数据库名称: bms
2025-07-21 11:12:42.539 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 启动dbaccess进程，数据库: bms
2025-07-21 11:12:42.543 [vgop-task-3vgop-task-3] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行dbaccess命令时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)
	at com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:157)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 16 common frames omitted
2025-07-21 11:12:42.544 [vgop-task-3vgop-task-3] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - dbaccess命令执行失败
2025-07-21 11:12:42.544 [vgop-task-3] ERROR c.v.s.service.VgopTaskScheduler [||||] [] - 统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.10-24205, 错误: 数据导出失败: UNLOAD执行失败
com.vgop.service.exception.TaskExecutionException: 数据导出失败: UNLOAD执行失败
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-21 13:41:44.249 [background-preinit] INFO  o.h.validator.internal.util.Version [||||] [] - HV000001: Hibernate Validator 6.2.0.Final
2025-07-21 13:41:44.358 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Starting VgopServiceApplication using Java 1.8.0_452 on galileo with PID 38116 (C:\workspaces\zjh\vgop\vgop-service\target\classes started by galil in C:\workspaces\zjh\vgop\vgop-service)
2025-07-21 13:41:44.359 [restartedMain] DEBUG c.v.service.VgopServiceApplication [||||] [] - Running with Spring Boot v2.5.5, Spring v5.3.19
2025-07-21 13:41:44.359 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - The following profiles are active: dev
2025-07-21 13:41:44.410 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor [||||] [] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-21 13:41:44.411 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor [||||] [] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-21 13:41:45.592 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\DataExportMapper.class]
2025-07-21 13:41:45.593 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\RevisionMapper.class]
2025-07-21 13:41:45.593 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\ValidationAlertsMapper.class]
2025-07-21 13:41:45.593 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\primary\RevisionTimesMapper.class]
2025-07-21 13:41:45.593 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\zjh\vgop\vgop-service\target\classes\com\vgop\service\dao\secondary\TaskExecutionMapper.class]
2025-07-21 13:41:45.594 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface
2025-07-21 13:41:45.597 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface
2025-07-21 13:41:45.597 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface
2025-07-21 13:41:45.597 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface
2025-07-21 13:41:45.597 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface
2025-07-21 13:41:45.840 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [||||] [] - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-21 13:41:46.481 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat initialized with port(s): 8080 (http)
2025-07-21 13:41:46.491 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-21 13:41:46.494 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Starting service [Tomcat]
2025-07-21 13:41:46.494 [restartedMain] INFO  o.a.catalina.core.StandardEngine [||||] [] - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-21 13:41:46.623 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring embedded WebApplicationContext
2025-07-21 13:41:46.623 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext [||||] [] - Root WebApplicationContext: initialization completed in 2212 ms
2025-07-21 13:41:46.816 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - ValidationEngine已初始化，等待规则配置...
2025-07-21 13:41:47.547 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\zjh\vgop\vgop-service\target\classes\mapper\DataExportMapper.xml]'
2025-07-21 13:41:47.562 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\zjh\vgop\vgop-service\target\classes\mapper\RevisionMapper.xml]'
2025-07-21 13:41:47.571 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\zjh\vgop\vgop-service\target\classes\mapper\TaskExecutionMapper.xml]'
2025-07-21 13:41:47.581 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\zjh\vgop\vgop-service\target\classes\mapper\ValidationAlertsMapper.xml]'
2025-07-21 13:41:47.746 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 开始测试数据库连接 ===
2025-07-21 13:41:47.746 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试主数据源连接...
2025-07-21 13:41:47.771 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-21 13:41:47.783 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-1} inited
2025-07-21 13:41:48.499 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 主数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=主数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-21 13:41:48.500 [restartedMain] INFO  com.vgop.service.util.DatabaseUtil [||||] [] - 检测到数据库产品: GBase Server, URL: jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
2025-07-21 13:41:48.500 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 检测到主数据库类型: gbase
2025-07-21 13:41:48.500 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 是否支持UNLOAD命令: true
2025-07-21 13:41:48.500 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试次数据源连接...
2025-07-21 13:41:48.501 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-21 13:41:48.502 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-2} inited
2025-07-21 13:41:49.122 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 次数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=次数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-21 13:41:49.122 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 数据库连接测试完成 ===
2025-07-21 13:41:49.479 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Using default implementation for ThreadExecutor
2025-07-21 13:41:49.499 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl [||||] [] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-21 13:41:49.500 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Quartz Scheduler v.2.3.2 created.
2025-07-21 13:41:49.503 [restartedMain] INFO  org.quartz.simpl.RAMJobStore [||||] [] - RAMJobStore initialized.
2025-07-21 13:41:49.504 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-21 13:41:49.505 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-21 13:41:49.505 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler version: 2.3.2
2025-07-21 13:41:49.505 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6a8e36e5
2025-07-21 13:41:49.562 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver [||||] [] - Exposing 15 endpoint(s) beneath base path '/actuator'
2025-07-21 13:41:49.661 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:49.665 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-21 13:41:49.665 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-21 13:41:49.665 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-21 13:41:49.667 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-21 13:41:49.667 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:49.772 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@4053d6ac]
2025-07-21 13:41:49.773 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:49.774 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-21 13:41:49.774 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@4053d6ac]: database product name is 'GBase Server'
2025-07-21 13:41:49.775 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-21 13:41:49.775 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-21 13:41:49.776 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-21 13:41:49.776 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-21 13:41:49.776 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-21 13:41:49.777 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:50.086 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-21 13:41:50.087 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-21 13:41:50.087 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:50.386 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-21 13:41:50.386 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置主数据源字符编码安全的JdbcTemplate
2025-07-21 13:41:50.387 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:50.387 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-21 13:41:50.387 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-21 13:41:50.388 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-21 13:41:50.388 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-21 13:41:50.388 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:50.456 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@7b989517]
2025-07-21 13:41:50.456 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:50.456 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-21 13:41:50.456 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@7b989517]: database product name is 'GBase Server'
2025-07-21 13:41:50.457 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-21 13:41:50.457 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-21 13:41:50.457 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-21 13:41:50.457 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-21 13:41:50.457 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-21 13:41:50.457 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:50.746 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-21 13:41:50.746 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-21 13:41:50.746 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-21 13:41:51.038 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-21 13:41:51.038 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置次数据源字符编码安全的JdbcTemplate
2025-07-21 13:41:51.221 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer [||||] [] - LiveReload server is running on port 35729
2025-07-21 13:41:51.797 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-21 13:41:51.858 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat started on port(s): 8080 (http) with context path '/vgop'
2025-07-21 13:41:52.606 [restartedMain] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Starting Quartz Scheduler now
2025-07-21 13:41:52.606 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-21 13:41:52.625 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Started VgopServiceApplication in 9.09 seconds (JVM running for 10.418)
2025-07-21 13:41:52.640 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 开始验证应用配置...
2025-07-21 13:41:52.641 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 配置验证通过
2025-07-21 13:41:52.641 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 开始初始化目录结构...
2025-07-21 13:41:52.642 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 数据导出根目录 - ./VGOPdata/datafile/
2025-07-21 13:41:52.642 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日志根目录 - ./logs/
2025-07-21 13:41:52.643 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 备份根目录 - ./data/backup/
2025-07-21 13:41:52.643 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 告警文件目录 - ./data/alerts/
2025-07-21 13:41:52.643 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250718 - ./VGOPdata/datafile//20250718/day
2025-07-21 13:41:52.644 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250718 - ./VGOPdata/datafile//20250718/month
2025-07-21 13:41:52.644 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250719 - ./VGOPdata/datafile//20250719/day
2025-07-21 13:41:52.645 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250719 - ./VGOPdata/datafile//20250719/month
2025-07-21 13:41:52.645 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250720 - ./VGOPdata/datafile//20250720/day
2025-07-21 13:41:52.645 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250720 - ./VGOPdata/datafile//20250720/month
2025-07-21 13:41:52.645 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250721 - ./VGOPdata/datafile//20250721/day
2025-07-21 13:41:52.646 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250721 - ./VGOPdata/datafile//20250721/month
2025-07-21 13:41:52.646 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250722 - ./VGOPdata/datafile//20250722/day
2025-07-21 13:41:52.647 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250722 - ./VGOPdata/datafile//20250722/month
2025-07-21 13:41:52.647 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250723 - ./VGOPdata/datafile//20250723/day
2025-07-21 13:41:52.647 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250723 - ./VGOPdata/datafile//20250723/month
2025-07-21 13:41:52.647 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月度数据目录 - 202507 - ./VGOPdata/datafile//202507
2025-07-21 13:41:52.648 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 目录结构初始化完成
2025-07-21 13:41:52.650 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 开始从配置文件加载校验规则...
2025-07-21 13:41:52.650 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24201 的校验规则...
2025-07-21 13:41:52.651 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.format - FORMAT
2025-07-21 13:41:52.652 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 13:41:52.652 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: FORMAT
2025-07-21 13:41:52.653 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.length - LENGTH
2025-07-21 13:41:52.653 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 13:41:52.653 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: LENGTH
2025-07-21 13:41:52.655 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonestate.enum - ENUM
2025-07-21 13:41:52.655 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonestate, 规则ID=common.phonestate.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.655 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonestate 添加了规则: ENUM
2025-07-21 13:41:52.656 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimsi.length - LENGTH
2025-07-21 13:41:52.656 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimsi, 规则ID=common.phoneimsi.length, 规则名称=长度校验
2025-07-21 13:41:52.656 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimsi 添加了规则: LENGTH
2025-07-21 13:41:52.656 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimei.length - LENGTH
2025-07-21 13:41:52.656 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimei, 规则ID=common.phoneimei.length, 规则名称=长度校验
2025-07-21 13:41:52.656 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimei 添加了规则: LENGTH
2025-07-21 13:41:52.656 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.locationid.length - LENGTH
2025-07-21 13:41:52.656 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=locationid, 规则ID=common.locationid.length, 规则名称=长度校验
2025-07-21 13:41:52.656 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 locationid 添加了规则: LENGTH
2025-07-21 13:41:52.656 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.provinceid.length - LENGTH
2025-07-21 13:41:52.657 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=provinceid, 规则ID=common.provinceid.length, 规则名称=长度校验
2025-07-21 13:41:52.657 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 provinceid 添加了规则: LENGTH
2025-07-21 13:41:52.657 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.openingtime.format - FORMAT
2025-07-21 13:41:52.657 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-21 13:41:52.657 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 openingtime 添加了规则: FORMAT
2025-07-21 13:41:52.657 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.optime.format - FORMAT
2025-07-21 13:41:52.657 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-21 13:41:52.657 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 Optime 添加了规则: FORMAT
2025-07-21 13:41:52.657 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.sex.enum - ENUM
2025-07-21 13:41:52.657 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=sex, 规则ID=common.sex.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.657 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 sex 添加了规则: ENUM
2025-07-21 13:41:52.658 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24201, 字段数量=9
2025-07-21 13:41:52.658 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了 9 个字段的元数据
2025-07-21 13:41:52.658 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [phonestate, Optime, phoneimei, openingtime, locationid, sex, phonenumber, phoneimsi, provinceid] -> VGOP1-R2-10-24201
2025-07-21 13:41:52.659 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了字段到接口的映射关系
2025-07-21 13:41:52.659 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24201 加载了 10 个字段校验规则
2025-07-21 13:41:52.659 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24202 的校验规则...
2025-07-21 13:41:52.659 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.format - FORMAT
2025-07-21 13:41:52.659 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-21 13:41:52.659 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: FORMAT
2025-07-21 13:41:52.659 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.length - LENGTH
2025-07-21 13:41:52.659 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-21 13:41:52.660 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: LENGTH
2025-07-21 13:41:52.660 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.format - FORMAT
2025-07-21 13:41:52.660 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 13:41:52.660 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: FORMAT
2025-07-21 13:41:52.660 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.length - LENGTH
2025-07-21 13:41:52.660 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 13:41:52.660 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: LENGTH
2025-07-21 13:41:52.660 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.business.enum - ENUM
2025-07-21 13:41:52.660 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=business, 规则ID=common.business.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.661 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 business 添加了规则: ENUM
2025-07-21 13:41:52.661 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.shutdown.length - LENGTH
2025-07-21 13:41:52.661 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-21 13:41:52.661 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 shutdown 添加了规则: LENGTH
2025-07-21 13:41:52.661 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.length - LENGTH
2025-07-21 13:41:52.661 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.length, 规则名称=长度校验
2025-07-21 13:41:52.661 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: LENGTH
2025-07-21 13:41:52.661 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.format - FORMAT
2025-07-21 13:41:52.661 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.format, 规则名称=格式校验
2025-07-21 13:41:52.661 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: FORMAT
2025-07-21 13:41:52.661 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnlocationid.length - LENGTH
2025-07-21 13:41:52.662 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnlocationid, 规则ID=common.mcnlocationid.length, 规则名称=长度校验
2025-07-21 13:41:52.662 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnlocationid 添加了规则: LENGTH
2025-07-21 13:41:52.662 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.numstate.length - LENGTH
2025-07-21 13:41:52.662 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=numstate, 规则ID=common.numstate.length, 规则名称=长度校验
2025-07-21 13:41:52.662 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 numstate 添加了规则: LENGTH
2025-07-21 13:41:52.662 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnature.enum - ENUM
2025-07-21 13:41:52.662 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnature, 规则ID=common.mcnnature.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.662 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnature 添加了规则: ENUM
2025-07-21 13:41:52.662 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.enum - ENUM
2025-07-21 13:41:52.664 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.664 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: ENUM
2025-07-21 13:41:52.664 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.length - LENGTH
2025-07-21 13:41:52.664 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.length, 规则名称=长度校验
2025-07-21 13:41:52.664 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: LENGTH
2025-07-21 13:41:52.664 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.channel.length - LENGTH
2025-07-21 13:41:52.664 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=channel, 规则ID=common.channel.length, 规则名称=长度校验
2025-07-21 13:41:52.664 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 channel 添加了规则: LENGTH
2025-07-21 13:41:52.664 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mj.enum - ENUM
2025-07-21 13:41:52.664 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mj, 规则ID=common.mj.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.664 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mj 添加了规则: ENUM
2025-07-21 13:41:52.664 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.openingtime.format - FORMAT
2025-07-21 13:41:52.664 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-21 13:41:52.665 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 openingtime 添加了规则: FORMAT
2025-07-21 13:41:52.665 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.optime.format - FORMAT
2025-07-21 13:41:52.665 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-21 13:41:52.665 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Optime 添加了规则: FORMAT
2025-07-21 13:41:52.665 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcimsitime.format - FORMAT
2025-07-21 13:41:52.665 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcimsitime, 规则ID=common.mcimsitime.format, 规则名称=格式校验
2025-07-21 13:41:52.665 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcimsitime 添加了规则: FORMAT
2025-07-21 13:41:52.665 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.usertype.length - LENGTH
2025-07-21 13:41:52.665 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=usertype, 规则ID=common.usertype.length, 规则名称=长度校验
2025-07-21 13:41:52.665 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 usertype 添加了规则: LENGTH
2025-07-21 13:41:52.665 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.begintime.format - FORMAT
2025-07-21 13:41:52.665 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Begintime, 规则ID=common.Begintime.format, 规则名称=格式校验
2025-07-21 13:41:52.666 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Begintime 添加了规则: FORMAT
2025-07-21 13:41:52.666 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.endtime.format - FORMAT
2025-07-21 13:41:52.666 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Endtime, 规则ID=common.Endtime.format, 规则名称=格式校验
2025-07-21 13:41:52.666 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Endtime 添加了规则: FORMAT
2025-07-21 13:41:52.666 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.servid.length - LENGTH
2025-07-21 13:41:52.666 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-21 13:41:52.666 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 ServID 添加了规则: LENGTH
2025-07-21 13:41:52.666 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24202, 字段数量=18
2025-07-21 13:41:52.666 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了 18 个字段的元数据
2025-07-21 13:41:52.666 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [mcnlocationid, Optime, business, mcnimsi, mcimsitime, phonenumber, mcnnum, channel, usertype, numstate, Endtime, openingtime, ServID, mcnnature, Begintime, mcnnumber, mj, shutdown] -> VGOP1-R2-10-24202
2025-07-21 13:41:52.667 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了字段到接口的映射关系
2025-07-21 13:41:52.667 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24202 加载了 22 个字段校验规则
2025-07-21 13:41:52.667 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24203 的校验规则...
2025-07-21 13:41:52.667 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.account_id.length - LENGTH
2025-07-21 13:41:52.667 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=account_id, 规则ID=common.account_id.length, 规则名称=长度校验
2025-07-21 13:41:52.667 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 account_id 添加了规则: LENGTH
2025-07-21 13:41:52.667 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.format - FORMAT
2025-07-21 13:41:52.667 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 13:41:52.667 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: FORMAT
2025-07-21 13:41:52.667 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.length - LENGTH
2025-07-21 13:41:52.667 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 13:41:52.667 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: LENGTH
2025-07-21 13:41:52.668 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.type.enum - ENUM
2025-07-21 13:41:52.668 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=type, 规则ID=common.type.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.668 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 type 添加了规则: ENUM
2025-07-21 13:41:52.668 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.optime.format - FORMAT
2025-07-21 13:41:52.668 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-21 13:41:52.668 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 optime 添加了规则: FORMAT
2025-07-21 13:41:52.668 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.version.length - LENGTH
2025-07-21 13:41:52.668 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=version, 规则ID=common.version.length, 规则名称=长度校验
2025-07-21 13:41:52.668 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 version 添加了规则: LENGTH
2025-07-21 13:41:52.669 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24203, 字段数量=5
2025-07-21 13:41:52.669 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了 5 个字段的元数据
2025-07-21 13:41:52.669 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [account_id, optime, phonenumber, type, version] -> VGOP1-R2-10-24203
2025-07-21 13:41:52.669 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了字段到接口的映射关系
2025-07-21 13:41:52.670 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24203 加载了 6 个字段校验规则
2025-07-21 13:41:52.670 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24205 的校验规则...
2025-07-21 13:41:52.670 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calltype.enum - ENUM
2025-07-21 13:41:52.670 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callType, 规则ID=common.callType.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.670 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callType 添加了规则: ENUM
2025-07-21 13:41:52.670 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.format - FORMAT
2025-07-21 13:41:52.670 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.format, 规则名称=格式校验
2025-07-21 13:41:52.670 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: FORMAT
2025-07-21 13:41:52.670 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.length - LENGTH
2025-07-21 13:41:52.670 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.length, 规则名称=长度校验
2025-07-21 13:41:52.670 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: LENGTH
2025-07-21 13:41:52.670 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.format - FORMAT
2025-07-21 13:41:52.670 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.format, 规则名称=格式校验
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: FORMAT
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.length - LENGTH
2025-07-21 13:41:52.671 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.length, 规则名称=长度校验
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: LENGTH
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.mcnnumber.format - FORMAT
2025-07-21 13:41:52.671 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 mcnnumber 添加了规则: FORMAT
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callbegintime.format - FORMAT
2025-07-21 13:41:52.671 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallBeginTime, 规则ID=common.CallBeginTime.format, 规则名称=格式校验
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallBeginTime 添加了规则: FORMAT
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callendtime.format - FORMAT
2025-07-21 13:41:52.671 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallEndTime, 规则ID=common.CallEndTime.format, 规则名称=格式校验
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallEndTime 添加了规则: FORMAT
2025-07-21 13:41:52.671 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.format - FORMAT
2025-07-21 13:41:52.672 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.format, 规则名称=格式校验
2025-07-21 13:41:52.672 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: FORMAT
2025-07-21 13:41:52.672 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.range - RANGE
2025-07-21 13:41:52.672 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.range, 规则名称=范围校验
2025-07-21 13:41:52.672 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: RANGE
2025-07-21 13:41:52.672 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24205, 字段数量=7
2025-07-21 13:41:52.673 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了 7 个字段的元数据
2025-07-21 13:41:52.673 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [CallEndTime, callingPartyNumber, calledPartyNumber, CallBeginTime, mcnnumber, CallDuration, callType] -> VGOP1-R2-10-24205
2025-07-21 13:41:52.673 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了字段到接口的映射关系
2025-07-21 13:41:52.673 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24205 加载了 10 个字段校验规则
2025-07-21 13:41:52.673 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24206 的校验规则...
2025-07-21 13:41:52.673 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.chargetype.enum - ENUM
2025-07-21 13:41:52.673 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=chargetype, 规则ID=common.chargetype.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.673 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 chargetype 添加了规则: ENUM
2025-07-21 13:41:52.673 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.format - FORMAT
2025-07-21 13:41:52.673 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 13:41:52.673 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: FORMAT
2025-07-21 13:41:52.674 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.length - LENGTH
2025-07-21 13:41:52.674 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 13:41:52.674 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: LENGTH
2025-07-21 13:41:52.674 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.mcnnumber.format - FORMAT
2025-07-21 13:41:52.674 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-21 13:41:52.674 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 mcnnumber 添加了规则: FORMAT
2025-07-21 13:41:52.674 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.format - FORMAT
2025-07-21 13:41:52.674 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.format, 规则名称=格式校验
2025-07-21 13:41:52.674 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: FORMAT
2025-07-21 13:41:52.674 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.length - LENGTH
2025-07-21 13:41:52.674 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.length, 规则名称=长度校验
2025-07-21 13:41:52.675 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: LENGTH
2025-07-21 13:41:52.675 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.optime.format - FORMAT
2025-07-21 13:41:52.675 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-21 13:41:52.675 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 optime 添加了规则: FORMAT
2025-07-21 13:41:52.675 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24206, 字段数量=5
2025-07-21 13:41:52.675 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了 5 个字段的元数据
2025-07-21 13:41:52.675 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [optime, phonenumber, chargetype, mcnnumber, sendorreceNum] -> VGOP1-R2-10-24206
2025-07-21 13:41:52.675 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了字段到接口的映射关系
2025-07-21 13:41:52.675 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24206 加载了 7 个字段校验规则
2025-07-21 13:41:52.675 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24207 的校验规则...
2025-07-21 13:41:52.676 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.format - FORMAT
2025-07-21 13:41:52.676 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-21 13:41:52.676 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: FORMAT
2025-07-21 13:41:52.676 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.length - LENGTH
2025-07-21 13:41:52.676 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-21 13:41:52.676 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: LENGTH
2025-07-21 13:41:52.676 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.format - FORMAT
2025-07-21 13:41:52.676 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-21 13:41:52.676 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: FORMAT
2025-07-21 13:41:52.677 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.length - LENGTH
2025-07-21 13:41:52.677 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-21 13:41:52.677 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: LENGTH
2025-07-21 13:41:52.677 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.format - FORMAT
2025-07-21 13:41:52.677 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.format, 规则名称=格式校验
2025-07-21 13:41:52.677 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: FORMAT
2025-07-21 13:41:52.677 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.length - LENGTH
2025-07-21 13:41:52.677 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.length, 规则名称=长度校验
2025-07-21 13:41:52.677 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: LENGTH
2025-07-21 13:41:52.677 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.businessstate.enum - ENUM
2025-07-21 13:41:52.677 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=businessState, 规则ID=common.businessState.enum, 规则名称=枚举值校验
2025-07-21 13:41:52.677 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 businessState 添加了规则: ENUM
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.numstate.format - FORMAT
2025-07-21 13:41:52.678 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Numstate, 规则ID=common.Numstate.format, 规则名称=格式校验
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Numstate 添加了规则: FORMAT
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.locationid.length - LENGTH
2025-07-21 13:41:52.678 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Locationid, 规则ID=common.Locationid.length, 规则名称=长度校验
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Locationid 添加了规则: LENGTH
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.format - FORMAT
2025-07-21 13:41:52.678 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.format, 规则名称=格式校验
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: FORMAT
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.range - RANGE
2025-07-21 13:41:52.678 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.range, 规则名称=范围校验
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: RANGE
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.openingtime.format - FORMAT
2025-07-21 13:41:52.678 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 openingtime 添加了规则: FORMAT
2025-07-21 13:41:52.678 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24207, 字段数量=8
2025-07-21 13:41:52.678 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了 8 个字段的元数据
2025-07-21 13:41:52.679 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [Numstate, openingtime, mcnImsi, businessState, BossProvinceid, phonenumber, Locationid, mcnnumber] -> VGOP1-R2-10-24207
2025-07-21 13:41:52.679 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了字段到接口的映射关系
2025-07-21 13:41:52.679 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24207 加载了 12 个字段校验规则
2025-07-21 13:41:52.679 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-11-24101 的校验规则...
2025-07-21 13:41:52.679 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.format - FORMAT
2025-07-21 13:41:52.679 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.format, 规则名称=格式校验
2025-07-21 13:41:52.679 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: FORMAT
2025-07-21 13:41:52.679 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.range - RANGE
2025-07-21 13:41:52.679 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.range, 规则名称=范围校验
2025-07-21 13:41:52.679 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: RANGE
2025-07-21 13:41:52.679 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.format - FORMAT
2025-07-21 13:41:52.679 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.format, 规则名称=格式校验
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: FORMAT
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.range - RANGE
2025-07-21 13:41:52.680 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.range, 规则名称=范围校验
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: RANGE
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.format - FORMAT
2025-07-21 13:41:52.680 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.format, 规则名称=格式校验
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: FORMAT
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.range - RANGE
2025-07-21 13:41:52.680 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.range, 规则名称=范围校验
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: RANGE
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.format - FORMAT
2025-07-21 13:41:52.680 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.format, 规则名称=格式校验
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: FORMAT
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.range - RANGE
2025-07-21 13:41:52.680 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.range, 规则名称=范围校验
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: RANGE
2025-07-21 13:41:52.680 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.format - FORMAT
2025-07-21 13:41:52.680 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.format, 规则名称=格式校验
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: FORMAT
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.range - RANGE
2025-07-21 13:41:52.681 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.range, 规则名称=范围校验
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: RANGE
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.format - FORMAT
2025-07-21 13:41:52.681 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.format, 规则名称=格式校验
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: FORMAT
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.range - RANGE
2025-07-21 13:41:52.681 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.range, 规则名称=范围校验
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: RANGE
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.format - FORMAT
2025-07-21 13:41:52.681 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.format, 规则名称=格式校验
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: FORMAT
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.range - RANGE
2025-07-21 13:41:52.681 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.range, 规则名称=范围校验
2025-07-21 13:41:52.681 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: RANGE
2025-07-21 13:41:52.682 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.format - FORMAT
2025-07-21 13:41:52.682 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.format, 规则名称=格式校验
2025-07-21 13:41:52.682 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: FORMAT
2025-07-21 13:41:52.682 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.range - RANGE
2025-07-21 13:41:52.682 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.range, 规则名称=范围校验
2025-07-21 13:41:52.682 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: RANGE
2025-07-21 13:41:52.682 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.format - FORMAT
2025-07-21 13:41:52.682 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.format, 规则名称=格式校验
2025-07-21 13:41:52.682 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: FORMAT
2025-07-21 13:41:52.682 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.range - RANGE
2025-07-21 13:41:52.682 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.range, 规则名称=范围校验
2025-07-21 13:41:52.682 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: RANGE
2025-07-21 13:41:52.682 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-11-24101, 字段数量=9
2025-07-21 13:41:52.682 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了 9 个字段的元数据
2025-07-21 13:41:52.683 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [appactivenum, paynum, mcnnum, secphonenum, phonenum, mcnactivenum, secmcnnum, amcnnum, feenum] -> VGOP1-R2-11-24101
2025-07-21 13:41:52.683 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了字段到接口的映射关系
2025-07-21 13:41:52.683 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-11-24101 加载了 18 个字段校验规则
2025-07-21 13:41:52.683 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24301 的校验规则...
2025-07-21 13:41:52.683 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.format - FORMAT
2025-07-21 13:41:52.683 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.format, 规则名称=格式校验
2025-07-21 13:41:52.683 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: FORMAT
2025-07-21 13:41:52.683 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.length - LENGTH
2025-07-21 13:41:52.683 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-21 13:41:52.683 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-21 13:41:52.683 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.standard_format - LENGTH
2025-07-21 13:41:52.683 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.length - LENGTH
2025-07-21 13:41:52.684 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.length, 规则名称=长度校验
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: LENGTH
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.content - FORMAT
2025-07-21 13:41:52.684 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.format, 规则名称=格式校验
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: FORMAT
2025-07-21 13:41:52.684 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24301, 字段数量=2
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了 2 个字段的元数据
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [ServName, ServID] -> VGOP1-R2-13-24301
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了字段到接口的映射关系
2025-07-21 13:41:52.684 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24301 加载了 5 个字段校验规则
2025-07-21 13:41:52.684 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24302 的校验规则...
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.format - FORMAT
2025-07-21 13:41:52.684 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.format, 规则名称=格式校验
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: FORMAT
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.length - LENGTH
2025-07-21 13:41:52.684 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.length, 规则名称=长度校验
2025-07-21 13:41:52.684 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: LENGTH
2025-07-21 13:41:52.685 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.range - RANGE
2025-07-21 13:41:52.685 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.range, 规则名称=范围校验
2025-07-21 13:41:52.685 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: RANGE
2025-07-21 13:41:52.685 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.length - LENGTH
2025-07-21 13:41:52.685 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.length, 规则名称=长度校验
2025-07-21 13:41:52.685 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: LENGTH
2025-07-21 13:41:52.685 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.content - FORMAT
2025-07-21 13:41:52.685 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.format, 规则名称=格式校验
2025-07-21 13:41:52.685 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: FORMAT
2025-07-21 13:41:52.685 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24302, 字段数量=2
2025-07-21 13:41:52.685 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了 2 个字段的元数据
2025-07-21 13:41:52.686 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [channelname, channelcode] -> VGOP1-R2-13-24302
2025-07-21 13:41:52.686 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了字段到接口的映射关系
2025-07-21 13:41:52.686 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24302 加载了 5 个字段校验规则
2025-07-21 13:41:52.686 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24303 的校验规则...
2025-07-21 13:41:52.686 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.format - FORMAT
2025-07-21 13:41:52.686 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.format, 规则名称=格式校验
2025-07-21 13:41:52.686 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: FORMAT
2025-07-21 13:41:52.686 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.length - LENGTH
2025-07-21 13:41:52.686 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-21 13:41:52.686 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: LENGTH
2025-07-21 13:41:52.686 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.range - RANGE
2025-07-21 13:41:52.687 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.range, 规则名称=范围校验
2025-07-21 13:41:52.687 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: RANGE
2025-07-21 13:41:52.687 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.length - LENGTH
2025-07-21 13:41:52.687 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.length, 规则名称=长度校验
2025-07-21 13:41:52.687 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: LENGTH
2025-07-21 13:41:52.687 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.content - FORMAT
2025-07-21 13:41:52.687 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.format, 规则名称=格式校验
2025-07-21 13:41:52.687 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: FORMAT
2025-07-21 13:41:52.687 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24303, 字段数量=2
2025-07-21 13:41:52.687 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了 2 个字段的元数据
2025-07-21 13:41:52.687 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [shutdownname, shutdown] -> VGOP1-R2-13-24303
2025-07-21 13:41:52.687 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了字段到接口的映射关系
2025-07-21 13:41:52.687 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24303 加载了 5 个字段校验规则
2025-07-21 13:41:52.688 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 校验规则加载完成，共加载 100 个规则
2025-07-21 13:42:05.140 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 13:42:05.140 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet [||||] [] - Initializing Servlet 'dispatcherServlet'
2025-07-21 13:42:05.142 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet [||||] [] - Completed initialization in 2 ms
2025-07-21 13:42:05.369 [http-nio-8080-exec-2] INFO  c.v.s.controller.VgopTaskController [||||] [] - 接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.10-24205
2025-07-21 13:42:08.284 [vgop-task-1] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.10-24205
2025-07-21 13:42:22.603 [vgop-task-1] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行数据导出任务: VGOP1-R2.10-24205
2025-07-21 13:42:28.904 [vgop-task-1vgop-task-1] INFO  c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20220214, revision=1
2025-07-21 13:42:28.914 [vgop-task-1vgop-task-1] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 日统计任务导出目录使用前一天日期: 20220214 -> 20220213
2025-07-21 13:42:28.916 [vgop-task-1vgop-task-1] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-21 13:42:28.917 [vgop-task-1vgop-task-1] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 临时文件路径: ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl
2025-07-21 13:42:28.919 [vgop-task-1vgop-task-1] INFO  c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行UNLOAD命令通过dbaccess: set lock mode to wait 10;
unload to ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl delimiter '|' select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-21 13:42:28.920 [vgop-task-1vgop-task-1] DEBUG com.vgop.service.util.DatabaseUtil [VGOP1-R2.10-24205|20220214|1||] [unload] - 从URL中提取的数据库名称: bms
2025-07-21 13:42:28.922 [vgop-task-1vgop-task-1] DEBUG c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 启动dbaccess进程，数据库: bms
2025-07-21 13:42:28.934 [vgop-task-1vgop-task-1] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行dbaccess命令时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)
	at com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:153)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 16 common frames omitted
2025-07-21 13:42:28.936 [vgop-task-1vgop-task-1] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - dbaccess命令执行失败
2025-07-21 13:42:42.428 [vgop-task-1] ERROR c.v.s.service.VgopTaskScheduler [||||] [] - 统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.10-24205, 错误: 数据导出失败: UNLOAD执行失败
com.vgop.service.exception.TaskExecutionException: 数据导出失败: UNLOAD执行失败
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-21 13:42:49.480 [http-nio-8080-exec-1] INFO  c.v.s.controller.VgopTaskController [||||] [] - 接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.10-24205
2025-07-21 13:42:51.120 [vgop-task-2] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.10-24205
2025-07-21 13:42:51.120 [vgop-task-2] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行数据导出任务: VGOP1-R2.10-24205
2025-07-21 13:42:53.293 [vgop-task-2vgop-task-2] INFO  c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20220214, revision=1
2025-07-21 13:44:33.544 [vgop-task-2vgop-task-2] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 日统计任务导出目录使用前一天日期: 20220214 -> 20220213
2025-07-21 13:44:55.336 [vgop-task-2vgop-task-2] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-21 13:45:27.722 [vgop-task-2vgop-task-2] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 临时文件路径: ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl
2025-07-21 13:45:42.872 [vgop-task-2vgop-task-2] INFO  c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行UNLOAD命令通过dbaccess: set lock mode to wait 10;
unload to ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl delimiter '|' select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-21 13:45:42.882 [vgop-task-2vgop-task-2] DEBUG com.vgop.service.util.DatabaseUtil [VGOP1-R2.10-24205|20220214|1||] [unload] - 从URL中提取的数据库名称: bms
2025-07-21 13:45:42.882 [vgop-task-2vgop-task-2] DEBUG c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 启动dbaccess进程，数据库: bms
2025-07-21 13:45:42.886 [vgop-task-2vgop-task-2] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行dbaccess命令时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)
	at com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:153)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 16 common frames omitted
2025-07-21 13:45:42.886 [vgop-task-2vgop-task-2] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - dbaccess命令执行失败
2025-07-21 13:45:42.886 [vgop-task-2] ERROR c.v.s.service.VgopTaskScheduler [||||] [] - 统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.10-24205, 错误: 数据导出失败: UNLOAD执行失败
com.vgop.service.exception.TaskExecutionException: 数据导出失败: UNLOAD执行失败
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-21 13:45:49.656 [http-nio-8080-exec-3] INFO  c.v.s.controller.VgopTaskController [||||] [] - 接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.10-24205
2025-07-21 13:45:50.839 [vgop-task-3] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.10-24205
2025-07-21 13:45:50.840 [vgop-task-3] INFO  c.v.s.service.VgopTaskScheduler [||||] [] - 开始执行数据导出任务: VGOP1-R2.10-24205
2025-07-21 13:45:54.007 [vgop-task-3vgop-task-3] INFO  c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20220214, revision=1
2025-07-21 13:45:54.008 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 日统计任务导出目录使用前一天日期: 20220214 -> 20220213
2025-07-21 13:45:54.008 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-21 13:45:54.008 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.DataExportService [VGOP1-R2.10-24205|20220214|1||] [export] - 临时文件路径: ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl
2025-07-21 13:46:09.810 [vgop-task-3vgop-task-3] INFO  c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行UNLOAD命令通过dbaccess: set lock mode to wait 10;
unload to ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl delimiter '|' select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'
2025-07-21 13:46:28.720 [vgop-task-3vgop-task-3] DEBUG com.vgop.service.util.DatabaseUtil [VGOP1-R2.10-24205|20220214|1||] [unload] - 从URL中提取的数据库名称: bms
2025-07-21 13:46:28.720 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 启动dbaccess进程，数据库: bms
2025-07-21 13:46:28.722 [vgop-task-3vgop-task-3] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行dbaccess命令时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)
	at com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:153)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 16 common frames omitted
2025-07-21 13:46:39.154 [vgop-task-3vgop-task-3] DEBUG com.vgop.service.util.DatabaseUtil [VGOP1-R2.10-24205|20220214|1||] [unload] - 从URL中提取的数据库名称: bms
2025-07-21 13:46:39.154 [vgop-task-3vgop-task-3] DEBUG c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 启动dbaccess进程，数据库: bms
2025-07-21 13:46:39.159 [vgop-task-3vgop-task-3] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - 执行dbaccess命令时发生异常
java.io.IOException: Cannot run program "dbaccess": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)
	at com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)
	at com.vgop.service.service.DataExportService.exportData(DataExportService.java:153)
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:459)
	at java.lang.ProcessImpl.start(ProcessImpl.java:139)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 16 common frames omitted
2025-07-21 13:46:39.159 [vgop-task-3vgop-task-3] ERROR c.v.s.service.UnloadExecutorService [VGOP1-R2.10-24205|20220214|1||] [unload] - dbaccess命令执行失败
2025-07-21 13:46:39.160 [vgop-task-3] ERROR c.v.s.service.VgopTaskScheduler [||||] [] - 统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.10-24205, 错误: 数据导出失败: UNLOAD执行失败
com.vgop.service.exception.TaskExecutionException: 数据导出失败: UNLOAD执行失败
	at com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)
	at com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
