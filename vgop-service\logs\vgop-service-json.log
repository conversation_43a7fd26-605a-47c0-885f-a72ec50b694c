{"@timestamp":"2025-07-21T11:11:01.704+08:00","@version":"1","message":"HV000001: Hibernate Validator 6.2.0.Final","logger_name":"org.hibernate.validator.internal.util.Version","thread_name":"background-preinit","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:01.825+08:00","@version":"1","message":"Starting VgopServiceApplication using Java 1.8.0_452 on galileo with PID 40684 (C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes started by galil in C:\\workspaces\\zjh\\vgop\\vgop-service)","logger_name":"com.vgop.service.VgopServiceApplication","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:01.826+08:00","@version":"1","message":"Running with Spring Boot v2.5.5, Spring v5.3.19","logger_name":"com.vgop.service.VgopServiceApplication","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:01.826+08:00","@version":"1","message":"The following profiles are active: dev","logger_name":"com.vgop.service.VgopServiceApplication","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:01.88+08:00","@version":"1","message":"Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable","logger_name":"org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:01.88+08:00","@version":"1","message":"For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'","logger_name":"org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.986+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\DataExportMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.987+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\RevisionMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.987+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\ValidationAlertsMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.987+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\primary\\RevisionTimesMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.987+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\secondary\\TaskExecutionMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.988+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.991+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.991+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.991+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:02.991+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:03.185+08:00","@version":"1","message":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:03.829+08:00","@version":"1","message":"Tomcat initialized with port(s): 8080 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:03.84+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-8080\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:03.843+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:03.843+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.83]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:03.97+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/vgop]","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:03.97+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 2089 ms","logger_name":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:04.16+08:00","@version":"1","message":"ValidationEngine已初始化，等待规则配置...","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:05.076+08:00","@version":"1","message":"Parsed mapper file: 'file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\mapper\\DataExportMapper.xml]'","logger_name":"org.mybatis.spring.SqlSessionFactoryBean","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:05.092+08:00","@version":"1","message":"Parsed mapper file: 'file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\mapper\\RevisionMapper.xml]'","logger_name":"org.mybatis.spring.SqlSessionFactoryBean","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:05.101+08:00","@version":"1","message":"Parsed mapper file: 'file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\mapper\\TaskExecutionMapper.xml]'","logger_name":"org.mybatis.spring.SqlSessionFactoryBean","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:05.111+08:00","@version":"1","message":"Parsed mapper file: 'file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\mapper\\ValidationAlertsMapper.xml]'","logger_name":"org.mybatis.spring.SqlSessionFactoryBean","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:05.287+08:00","@version":"1","message":"=== 开始测试数据库连接 ===","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:05.288+08:00","@version":"1","message":"测试主数据源连接...","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:05.316+08:00","@version":"1","message":"testWhileIdle is true, validationQuery not set","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"restartedMain","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:05.333+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:06.207+08:00","@version":"1","message":"主数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=主数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:06.207+08:00","@version":"1","message":"检测到数据库产品: GBase Server, URL: jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;","logger_name":"com.vgop.service.util.DatabaseUtil","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:06.207+08:00","@version":"1","message":"检测到主数据库类型: gbase","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:06.207+08:00","@version":"1","message":"是否支持UNLOAD命令: true","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:06.208+08:00","@version":"1","message":"测试次数据源连接...","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:06.208+08:00","@version":"1","message":"testWhileIdle is true, validationQuery not set","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"restartedMain","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:06.209+08:00","@version":"1","message":"{dataSource-2} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.022+08:00","@version":"1","message":"次数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=次数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.023+08:00","@version":"1","message":"=== 数据库连接测试完成 ===","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.384+08:00","@version":"1","message":"Using default implementation for ThreadExecutor","logger_name":"org.quartz.impl.StdSchedulerFactory","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.403+08:00","@version":"1","message":"Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl","logger_name":"org.quartz.core.SchedulerSignalerImpl","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.404+08:00","@version":"1","message":"Quartz Scheduler v.2.3.2 created.","logger_name":"org.quartz.core.QuartzScheduler","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.408+08:00","@version":"1","message":"RAMJobStore initialized.","logger_name":"org.quartz.simpl.RAMJobStore","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.409+08:00","@version":"1","message":"Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n","logger_name":"org.quartz.core.QuartzScheduler","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.409+08:00","@version":"1","message":"Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.","logger_name":"org.quartz.impl.StdSchedulerFactory","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.409+08:00","@version":"1","message":"Quartz scheduler version: 2.3.2","logger_name":"org.quartz.impl.StdSchedulerFactory","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.41+08:00","@version":"1","message":"JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@332b8db3","logger_name":"org.quartz.core.QuartzScheduler","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.465+08:00","@version":"1","message":"Exposing 15 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.566+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.571+08:00","@version":"1","message":"数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.571+08:00","@version":"1","message":"检测到数据库类型: GBase Server","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.571+08:00","@version":"1","message":"配置GBase数据库字符编码设置","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.573+08:00","@version":"1","message":"Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.574+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.697+08:00","@version":"1","message":"Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@289eba1a]","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.698+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.698+08:00","@version":"1","message":"SQL error codes for 'GBase Server' not found","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.698+08:00","@version":"1","message":"Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@289eba1a]: database product name is 'GBase Server'","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.699+08:00","@version":"1","message":"Unable to translate SQLException with Error code '-728', will now try the fallback translator","logger_name":"org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.699+08:00","@version":"1","message":"Extracted SQL state class 'IX' from value 'IX000'","logger_name":"org.springframework.jdbc.support.SQLStateSQLExceptionTranslator","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.7+08:00","@version":"1","message":"查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.7+08:00","@version":"1","message":"GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.7+08:00","@version":"1","message":"Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:07.7+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.078+08:00","@version":"1","message":"数据库主机名: eb33207","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.079+08:00","@version":"1","message":"Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.079+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.459+08:00","@version":"1","message":"字符编码兼容性测试通过，中文字符长度: 12","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.459+08:00","@version":"1","message":"已配置主数据源字符编码安全的JdbcTemplate","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.46+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.461+08:00","@version":"1","message":"数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.461+08:00","@version":"1","message":"检测到数据库类型: GBase Server","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.461+08:00","@version":"1","message":"配置GBase数据库字符编码设置","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.461+08:00","@version":"1","message":"Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.461+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.53+08:00","@version":"1","message":"Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@444462b4]","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.53+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.53+08:00","@version":"1","message":"SQL error codes for 'GBase Server' not found","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.531+08:00","@version":"1","message":"Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@444462b4]: database product name is 'GBase Server'","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.531+08:00","@version":"1","message":"Unable to translate SQLException with Error code '-728', will now try the fallback translator","logger_name":"org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.531+08:00","@version":"1","message":"Extracted SQL state class 'IX' from value 'IX000'","logger_name":"org.springframework.jdbc.support.SQLStateSQLExceptionTranslator","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.531+08:00","@version":"1","message":"查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.531+08:00","@version":"1","message":"GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.531+08:00","@version":"1","message":"Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:08.531+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:09.048+08:00","@version":"1","message":"数据库主机名: eb33207","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:09.048+08:00","@version":"1","message":"Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:09.048+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:09.492+08:00","@version":"1","message":"字符编码兼容性测试通过，中文字符长度: 12","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:09.492+08:00","@version":"1","message":"已配置次数据源字符编码安全的JdbcTemplate","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:09.585+08:00","@version":"1","message":"LiveReload server is running on port 35729","logger_name":"org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.066+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-8080\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.136+08:00","@version":"1","message":"Tomcat started on port(s): 8080 (http) with context path '/vgop'","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.852+08:00","@version":"1","message":"Starting Quartz Scheduler now","logger_name":"org.springframework.scheduling.quartz.SchedulerFactoryBean","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.853+08:00","@version":"1","message":"Scheduler quartzScheduler_$_NON_CLUSTERED started.","logger_name":"org.quartz.core.QuartzScheduler","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.876+08:00","@version":"1","message":"Started VgopServiceApplication in 9.957 seconds (JVM running for 11.543)","logger_name":"com.vgop.service.VgopServiceApplication","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.885+08:00","@version":"1","message":"开始验证应用配置...","logger_name":"com.vgop.service.service.ConfigValidationService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.886+08:00","@version":"1","message":"配置验证通过","logger_name":"com.vgop.service.service.ConfigValidationService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.886+08:00","@version":"1","message":"开始初始化目录结构...","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.886+08:00","@version":"1","message":"目录已存在: 数据导出根目录 - ./VGOPdata/datafile/","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.886+08:00","@version":"1","message":"目录已存在: 日志根目录 - ./logs/","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.886+08:00","@version":"1","message":"目录已存在: 备份根目录 - ./data/backup/","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.886+08:00","@version":"1","message":"目录已存在: 告警文件目录 - ./data/alerts/","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.887+08:00","@version":"1","message":"目录已存在: 日数据目录 - 20250718 - ./VGOPdata/datafile//20250718/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.887+08:00","@version":"1","message":"目录已存在: 月数据目录 - 20250718 - ./VGOPdata/datafile//20250718/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.887+08:00","@version":"1","message":"目录已存在: 日数据目录 - 20250719 - ./VGOPdata/datafile//20250719/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.887+08:00","@version":"1","message":"目录已存在: 月数据目录 - 20250719 - ./VGOPdata/datafile//20250719/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.921+08:00","@version":"1","message":"创建目录成功: 日数据目录 - 20250720 - ./VGOPdata/datafile//20250720/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.922+08:00","@version":"1","message":"创建目录成功: 月数据目录 - 20250720 - ./VGOPdata/datafile//20250720/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.925+08:00","@version":"1","message":"创建目录成功: 日数据目录 - 20250721 - ./VGOPdata/datafile//20250721/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.927+08:00","@version":"1","message":"创建目录成功: 月数据目录 - 20250721 - ./VGOPdata/datafile//20250721/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.929+08:00","@version":"1","message":"创建目录成功: 日数据目录 - 20250722 - ./VGOPdata/datafile//20250722/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.931+08:00","@version":"1","message":"创建目录成功: 月数据目录 - 20250722 - ./VGOPdata/datafile//20250722/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.933+08:00","@version":"1","message":"创建目录成功: 日数据目录 - 20250723 - ./VGOPdata/datafile//20250723/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.934+08:00","@version":"1","message":"创建目录成功: 月数据目录 - 20250723 - ./VGOPdata/datafile//20250723/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.934+08:00","@version":"1","message":"目录已存在: 月度数据目录 - 202507 - ./VGOPdata/datafile//202507","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.934+08:00","@version":"1","message":"目录结构初始化完成","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.936+08:00","@version":"1","message":"开始从配置文件加载校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.937+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24201 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.938+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.938+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.939+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.939+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.939+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.939+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.939+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phonestate.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.94+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonestate, 规则ID=common.phonestate.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.94+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phonestate 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.94+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phoneimsi.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.94+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimsi, 规则ID=common.phoneimsi.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.94+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phoneimsi 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.94+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phoneimei.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.94+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimei, 规则ID=common.phoneimei.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phoneimei 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.locationid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=locationid, 规则ID=common.locationid.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 locationid 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.provinceid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=provinceid, 规则ID=common.provinceid.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 provinceid 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.openingtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 openingtime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.optime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.941+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 Optime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.sex.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=sex, 规则ID=common.sex.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 sex 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24201, 字段数量=9","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 注册了 9 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"注册字段到接口映射: [phonestate, Optime, phoneimei, openingtime, locationid, sex, phonenumber, phoneimsi, provinceid] -> VGOP1-R2-10-24201","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"接口 VGOP1-R2-10-24201 加载了 10 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24202 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.942+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.business.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=business, 规则ID=common.business.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 business 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.shutdown.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.943+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 shutdown 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnlocationid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnlocationid, 规则ID=common.mcnlocationid.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnlocationid 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.numstate.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=numstate, 规则ID=common.numstate.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 numstate 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnature.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnature, 规则ID=common.mcnnature.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnature 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnum.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.944+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnum.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.channel.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=channel, 规则ID=common.channel.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 channel 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mj.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mj, 规则ID=common.mj.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mj 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.945+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.openingtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.946+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.946+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 openingtime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.946+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.optime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.946+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.946+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 Optime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.946+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcimsitime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.946+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcimsitime, 规则ID=common.mcimsitime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.946+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcimsitime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.usertype.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=usertype, 规则ID=common.usertype.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 usertype 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.begintime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Begintime, 规则ID=common.Begintime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 Begintime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.endtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Endtime, 规则ID=common.Endtime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 Endtime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.servid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.947+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.948+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 ServID 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.948+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24202, 字段数量=18","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.948+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 注册了 18 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.948+08:00","@version":"1","message":"注册字段到接口映射: [mcnlocationid, Optime, business, mcnimsi, mcimsitime, phonenumber, mcnnum, channel, usertype, numstate, Endtime, openingtime, ServID, mcnnature, Begintime, mcnnumber, mj, shutdown] -> VGOP1-R2-10-24202","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.948+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.948+08:00","@version":"1","message":"接口 VGOP1-R2-10-24202 加载了 22 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.948+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24203 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.948+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.account_id.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.948+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=account_id, 规则ID=common.account_id.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 account_id 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.type.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=type, 规则ID=common.type.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.949+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 type 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.optime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 optime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.version.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=version, 规则ID=common.version.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 version 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24203, 字段数量=5","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 注册了 5 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"注册字段到接口映射: [account_id, optime, phonenumber, type, version] -> VGOP1-R2-10-24203","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.95+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.951+08:00","@version":"1","message":"接口 VGOP1-R2-10-24203 加载了 6 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.951+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24205 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.951+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.calltype.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.951+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callType, 规则ID=common.callType.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.951+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 callType 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.951+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.951+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.952+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.952+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.952+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.952+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.952+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.952+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.952+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.953+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.953+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.953+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.953+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.mcnnumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.953+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.953+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 mcnnumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.953+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callbegintime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.953+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallBeginTime, 规则ID=common.CallBeginTime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.954+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 CallBeginTime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.954+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callendtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.954+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallEndTime, 规则ID=common.CallEndTime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.954+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 CallEndTime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.954+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callduration.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.954+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.954+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.954+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callduration.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.955+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.955+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.955+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24205, 字段数量=7","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.955+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 注册了 7 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.955+08:00","@version":"1","message":"注册字段到接口映射: [CallEndTime, callingPartyNumber, calledPartyNumber, CallBeginTime, mcnnumber, CallDuration, callType] -> VGOP1-R2-10-24205","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.955+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.955+08:00","@version":"1","message":"接口 VGOP1-R2-10-24205 加载了 10 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.955+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24206 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.chargetype.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=chargetype, 规则ID=common.chargetype.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 chargetype 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.mcnnumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.956+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 mcnnumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.optime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 optime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.957+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24206, 字段数量=5","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 注册了 5 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"注册字段到接口映射: [optime, phonenumber, chargetype, mcnnumber, sendorreceNum] -> VGOP1-R2-10-24206","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"接口 VGOP1-R2-10-24206 加载了 7 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24207 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.958+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.businessstate.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=businessState, 规则ID=common.businessState.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 businessState 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.numstate.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Numstate, 规则ID=common.Numstate.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 Numstate 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.locationid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Locationid, 规则ID=common.Locationid.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 Locationid 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.959+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.openingtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 openingtime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24207, 字段数量=8","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 注册了 8 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"注册字段到接口映射: [Numstate, openingtime, mcnImsi, businessState, BossProvinceid, phonenumber, Locationid, mcnnumber] -> VGOP1-R2-10-24207","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"接口 VGOP1-R2-10-24207 加载了 12 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-11-24101 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.96+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.phonenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.phonenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.mcnnum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.mcnnum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.appactivenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.appactivenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.paynum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.paynum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.feenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.961+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.feenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.secphonenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.secphonenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.amcnnum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.amcnnum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-11-24101, 字段数量=9","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 注册了 9 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"注册字段到接口映射: [appactivenum, paynum, mcnnum, secphonenum, phonenum, mcnactivenum, secmcnnum, amcnnum, feenum] -> VGOP1-R2-11-24101","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"接口 VGOP1-R2-11-24101 加载了 18 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-13-24301 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servid.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.962+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servid.standard_format - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servname.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servname.content - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-13-24301, 字段数量=2","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 注册了 2 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"注册字段到接口映射: [ServName, ServID] -> VGOP1-R2-13-24301","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"接口 VGOP1-R2-13-24301 加载了 5 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-13-24302 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.963+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelcode.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelcode.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelcode.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelname.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelname.content - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-13-24302, 字段数量=2","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 注册了 2 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"注册字段到接口映射: [channelname, channelcode] -> VGOP1-R2-13-24302","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"接口 VGOP1-R2-13-24302 加载了 5 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-13-24303 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdown.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdown.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.964+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdown.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdownname.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdownname.content - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-13-24303, 字段数量=2","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 注册了 2 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"注册字段到接口映射: [shutdownname, shutdown] -> VGOP1-R2-13-24303","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"接口 VGOP1-R2-13-24303 加载了 5 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:10.965+08:00","@version":"1","message":"校验规则加载完成，共加载 100 个规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.434+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/vgop]","thread_name":"http-nio-8080-exec-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.434+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8080-exec-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.436+08:00","@version":"1","message":"Completed initialization in 2 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8080-exec-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.65+08:00","@version":"1","message":"接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.11-24101","logger_name":"com.vgop.service.controller.VgopTaskController","thread_name":"http-nio-8080-exec-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.671+08:00","@version":"1","message":"开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.11-24101","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.672+08:00","@version":"1","message":"开始执行数据导出任务: VGOP1-R2.11-24101","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.672+08:00","@version":"1","message":"VGOP业务分析任务执行顺序 - 步骤1: 即将通过dbaccess调用存储过程 bmssp_VGOP_banalyse","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.673+08:00","@version":"1","message":"开始导出接口数据: interfaceId=VGOP1-R2.11-24101, dataDate=20250622, revision=1","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.675+08:00","@version":"1","message":"检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.675+08:00","@version":"1","message":"调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20250622","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.676+08:00","@version":"1","message":"从URL中提取的数据库名称: bms","logger_name":"com.vgop.service.util.DatabaseUtil","thread_name":"vgop-task-1","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.676+08:00","@version":"1","message":"通过dbaccess执行存储过程，数据库: bms","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.676+08:00","@version":"1","message":"存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse(\"\",\"0\",\"20250622000000\");","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.686+08:00","@version":"1","message":"通过dbaccess执行存储过程时发生异常","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: Cannot run program \"dbaccess\": CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)\r\n\tat com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:298)\r\n\tat com.vgop.service.service.DataExportService.exportData(DataExportService.java:84)\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\nCaused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessImpl.create(Native Method)\r\n\tat java.lang.ProcessImpl.<init>(ProcessImpl.java:459)\r\n\tat java.lang.ProcessImpl.start(ProcessImpl.java:139)\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)\r\n\t... 15 common frames omitted\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.687+08:00","@version":"1","message":"存储过程 bmssp_VGOP_banalyse 执行失败","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:18.687+08:00","@version":"1","message":"统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.11-24101, 错误: 数据导出失败: 存储过程执行失败","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-1","level":"ERROR","level_value":40000,"stack_trace":"com.vgop.service.exception.TaskExecutionException: 数据导出失败: 存储过程执行失败\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:28.443+08:00","@version":"1","message":"接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.11-24101","logger_name":"com.vgop.service.controller.VgopTaskController","thread_name":"http-nio-8080-exec-6","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:28.443+08:00","@version":"1","message":"开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.11-24101","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:28.444+08:00","@version":"1","message":"开始执行数据导出任务: VGOP1-R2.11-24101","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:28.444+08:00","@version":"1","message":"VGOP业务分析任务执行顺序 - 步骤1: 即将通过dbaccess调用存储过程 bmssp_VGOP_banalyse","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:42.184+08:00","@version":"1","message":"开始导出接口数据: interfaceId=VGOP1-R2.11-24101, dataDate=20250622, revision=1","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:46.408+08:00","@version":"1","message":"检测到VGOP业务分析任务，执行存储过程调用: VGOP1-R2.11-24101","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:47.846+08:00","@version":"1","message":"调用存储过程 bmssp_VGOP_banalyse: debugFile=, traceFlag=0, taskId=20250622","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:47.846+08:00","@version":"1","message":"从URL中提取的数据库名称: bms","logger_name":"com.vgop.service.util.DatabaseUtil","thread_name":"vgop-task-2","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:47.846+08:00","@version":"1","message":"通过dbaccess执行存储过程，数据库: bms","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:47.846+08:00","@version":"1","message":"存储过程SQL命令: set lock mode to wait 10;call bmssp_VGOP_banalyse(\"\",\"0\",\"20250622000000\");","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:47.851+08:00","@version":"1","message":"通过dbaccess执行存储过程时发生异常","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: Cannot run program \"dbaccess\": CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)\r\n\tat com.vgop.service.service.DataExportService.executeStoredProcedureViaDbAccess(DataExportService.java:298)\r\n\tat com.vgop.service.service.DataExportService.exportData(DataExportService.java:84)\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\nCaused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessImpl.create(Native Method)\r\n\tat java.lang.ProcessImpl.<init>(ProcessImpl.java:459)\r\n\tat java.lang.ProcessImpl.start(ProcessImpl.java:139)\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)\r\n\t... 15 common frames omitted\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:47.852+08:00","@version":"1","message":"存储过程 bmssp_VGOP_banalyse 执行失败","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20250622","operationType":"export","interfaceId":"VGOP1-R2.11-24101","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:47.853+08:00","@version":"1","message":"统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.11-24101, 错误: 数据导出失败: 存储过程执行失败","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-2","level":"ERROR","level_value":40000,"stack_trace":"com.vgop.service.exception.TaskExecutionException: 数据导出失败: 存储过程执行失败\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:54.682+08:00","@version":"1","message":"接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.10-24205","logger_name":"com.vgop.service.controller.VgopTaskController","thread_name":"http-nio-8080-exec-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:54.683+08:00","@version":"1","message":"开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.10-24205","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:54.683+08:00","@version":"1","message":"开始执行数据导出任务: VGOP1-R2.10-24205","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:11:56.696+08:00","@version":"1","message":"开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20220214, revision=1","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.535+08:00","@version":"1","message":"构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.536+08:00","@version":"1","message":"日统计任务导出目录使用前一天日期: 20220214 -> 20220213","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.538+08:00","@version":"1","message":"创建导出目录: ./VGOPdata/datafile/20220213/daily/, 结果: true","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.538+08:00","@version":"1","message":"临时文件路径: ./VGOPdata/datafile/20220213/daily/a_10000_20220214_VGOP1-R2.10-24205.unl","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.539+08:00","@version":"1","message":"执行UNLOAD命令通过dbaccess: set lock mode to wait 10;\nunload to ./VGOPdata/datafile/20220213/daily/a_10000_20220214_VGOP1-R2.10-24205.unl delimiter '|' select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.539+08:00","@version":"1","message":"从URL中提取的数据库名称: bms","logger_name":"com.vgop.service.util.DatabaseUtil","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.539+08:00","@version":"1","message":"启动dbaccess进程，数据库: bms","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.543+08:00","@version":"1","message":"执行dbaccess命令时发生异常","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: Cannot run program \"dbaccess\": CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)\r\n\tat com.vgop.service.service.DataExportService.exportData(DataExportService.java:157)\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\nCaused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessImpl.create(Native Method)\r\n\tat java.lang.ProcessImpl.<init>(ProcessImpl.java:459)\r\n\tat java.lang.ProcessImpl.start(ProcessImpl.java:139)\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)\r\n\t... 16 common frames omitted\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.544+08:00","@version":"1","message":"dbaccess命令执行失败","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T11:12:42.544+08:00","@version":"1","message":"统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.10-24205, 错误: 数据导出失败: UNLOAD执行失败","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-3","level":"ERROR","level_value":40000,"stack_trace":"com.vgop.service.exception.TaskExecutionException: 数据导出失败: UNLOAD执行失败\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:44.249+08:00","@version":"1","message":"HV000001: Hibernate Validator 6.2.0.Final","logger_name":"org.hibernate.validator.internal.util.Version","thread_name":"background-preinit","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:44.358+08:00","@version":"1","message":"Starting VgopServiceApplication using Java 1.8.0_452 on galileo with PID 38116 (C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes started by galil in C:\\workspaces\\zjh\\vgop\\vgop-service)","logger_name":"com.vgop.service.VgopServiceApplication","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:44.359+08:00","@version":"1","message":"Running with Spring Boot v2.5.5, Spring v5.3.19","logger_name":"com.vgop.service.VgopServiceApplication","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:44.359+08:00","@version":"1","message":"The following profiles are active: dev","logger_name":"com.vgop.service.VgopServiceApplication","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:44.41+08:00","@version":"1","message":"Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable","logger_name":"org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:44.411+08:00","@version":"1","message":"For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'","logger_name":"org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.592+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\DataExportMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.593+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\RevisionMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.593+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\ValidationAlertsMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.593+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\primary\\RevisionTimesMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.593+08:00","@version":"1","message":"Identified candidate component class: file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\com\\vgop\\service\\dao\\secondary\\TaskExecutionMapper.class]","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.594+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.597+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.597+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.597+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.597+08:00","@version":"1","message":"Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:45.84+08:00","@version":"1","message":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:46.481+08:00","@version":"1","message":"Tomcat initialized with port(s): 8080 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:46.491+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-8080\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:46.494+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:46.494+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.83]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:46.623+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/vgop]","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:46.623+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 2212 ms","logger_name":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:46.816+08:00","@version":"1","message":"ValidationEngine已初始化，等待规则配置...","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:47.547+08:00","@version":"1","message":"Parsed mapper file: 'file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\mapper\\DataExportMapper.xml]'","logger_name":"org.mybatis.spring.SqlSessionFactoryBean","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:47.562+08:00","@version":"1","message":"Parsed mapper file: 'file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\mapper\\RevisionMapper.xml]'","logger_name":"org.mybatis.spring.SqlSessionFactoryBean","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:47.571+08:00","@version":"1","message":"Parsed mapper file: 'file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\mapper\\TaskExecutionMapper.xml]'","logger_name":"org.mybatis.spring.SqlSessionFactoryBean","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:47.581+08:00","@version":"1","message":"Parsed mapper file: 'file [C:\\workspaces\\zjh\\vgop\\vgop-service\\target\\classes\\mapper\\ValidationAlertsMapper.xml]'","logger_name":"org.mybatis.spring.SqlSessionFactoryBean","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","tags":["MYBATIS"],"application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:47.746+08:00","@version":"1","message":"=== 开始测试数据库连接 ===","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:47.746+08:00","@version":"1","message":"测试主数据源连接...","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:47.771+08:00","@version":"1","message":"testWhileIdle is true, validationQuery not set","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"restartedMain","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:47.783+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:48.499+08:00","@version":"1","message":"主数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=主数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:48.5+08:00","@version":"1","message":"检测到数据库产品: GBase Server, URL: jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;","logger_name":"com.vgop.service.util.DatabaseUtil","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:48.5+08:00","@version":"1","message":"检测到主数据库类型: gbase","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:48.5+08:00","@version":"1","message":"是否支持UNLOAD命令: true","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:48.5+08:00","@version":"1","message":"测试次数据源连接...","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:48.501+08:00","@version":"1","message":"testWhileIdle is true, validationQuery not set","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"restartedMain","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:48.502+08:00","@version":"1","message":"{dataSource-2} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.122+08:00","@version":"1","message":"次数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=次数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.122+08:00","@version":"1","message":"=== 数据库连接测试完成 ===","logger_name":"com.vgop.service.service.DatabaseTestService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.479+08:00","@version":"1","message":"Using default implementation for ThreadExecutor","logger_name":"org.quartz.impl.StdSchedulerFactory","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.499+08:00","@version":"1","message":"Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl","logger_name":"org.quartz.core.SchedulerSignalerImpl","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.5+08:00","@version":"1","message":"Quartz Scheduler v.2.3.2 created.","logger_name":"org.quartz.core.QuartzScheduler","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.503+08:00","@version":"1","message":"RAMJobStore initialized.","logger_name":"org.quartz.simpl.RAMJobStore","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.504+08:00","@version":"1","message":"Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n","logger_name":"org.quartz.core.QuartzScheduler","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.505+08:00","@version":"1","message":"Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.","logger_name":"org.quartz.impl.StdSchedulerFactory","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.505+08:00","@version":"1","message":"Quartz scheduler version: 2.3.2","logger_name":"org.quartz.impl.StdSchedulerFactory","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.505+08:00","@version":"1","message":"JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6a8e36e5","logger_name":"org.quartz.core.QuartzScheduler","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.562+08:00","@version":"1","message":"Exposing 15 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.661+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.665+08:00","@version":"1","message":"数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.665+08:00","@version":"1","message":"检测到数据库类型: GBase Server","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.665+08:00","@version":"1","message":"配置GBase数据库字符编码设置","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.667+08:00","@version":"1","message":"Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.667+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.772+08:00","@version":"1","message":"Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@4053d6ac]","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.773+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.774+08:00","@version":"1","message":"SQL error codes for 'GBase Server' not found","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.774+08:00","@version":"1","message":"Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@4053d6ac]: database product name is 'GBase Server'","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.775+08:00","@version":"1","message":"Unable to translate SQLException with Error code '-728', will now try the fallback translator","logger_name":"org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.775+08:00","@version":"1","message":"Extracted SQL state class 'IX' from value 'IX000'","logger_name":"org.springframework.jdbc.support.SQLStateSQLExceptionTranslator","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.776+08:00","@version":"1","message":"查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.776+08:00","@version":"1","message":"GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.776+08:00","@version":"1","message":"Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:49.777+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.086+08:00","@version":"1","message":"数据库主机名: eb33207","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.087+08:00","@version":"1","message":"Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.087+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.386+08:00","@version":"1","message":"字符编码兼容性测试通过，中文字符长度: 12","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.386+08:00","@version":"1","message":"已配置主数据源字符编码安全的JdbcTemplate","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.387+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.387+08:00","@version":"1","message":"数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.387+08:00","@version":"1","message":"检测到数据库类型: GBase Server","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.388+08:00","@version":"1","message":"配置GBase数据库字符编码设置","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.388+08:00","@version":"1","message":"Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.388+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.456+08:00","@version":"1","message":"Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@7b989517]","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.456+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.456+08:00","@version":"1","message":"SQL error codes for 'GBase Server' not found","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.456+08:00","@version":"1","message":"Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@7b989517]: database product name is 'GBase Server'","logger_name":"org.springframework.jdbc.support.SQLErrorCodesFactory","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.457+08:00","@version":"1","message":"Unable to translate SQLException with Error code '-728', will now try the fallback translator","logger_name":"org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.457+08:00","@version":"1","message":"Extracted SQL state class 'IX' from value 'IX000'","logger_name":"org.springframework.jdbc.support.SQLStateSQLExceptionTranslator","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.457+08:00","@version":"1","message":"查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.457+08:00","@version":"1","message":"GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.457+08:00","@version":"1","message":"Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.457+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.746+08:00","@version":"1","message":"数据库主机名: eb33207","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.746+08:00","@version":"1","message":"Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:50.746+08:00","@version":"1","message":"Fetching JDBC Connection from DataSource","logger_name":"org.springframework.jdbc.datasource.DataSourceUtils","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:51.038+08:00","@version":"1","message":"字符编码兼容性测试通过，中文字符长度: 12","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:51.038+08:00","@version":"1","message":"已配置次数据源字符编码安全的JdbcTemplate","logger_name":"com.vgop.service.config.DatabaseCharsetConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:51.221+08:00","@version":"1","message":"LiveReload server is running on port 35729","logger_name":"org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:51.797+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-8080\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:51.858+08:00","@version":"1","message":"Tomcat started on port(s): 8080 (http) with context path '/vgop'","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.606+08:00","@version":"1","message":"Starting Quartz Scheduler now","logger_name":"org.springframework.scheduling.quartz.SchedulerFactoryBean","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.606+08:00","@version":"1","message":"Scheduler quartzScheduler_$_NON_CLUSTERED started.","logger_name":"org.quartz.core.QuartzScheduler","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.625+08:00","@version":"1","message":"Started VgopServiceApplication in 9.09 seconds (JVM running for 10.418)","logger_name":"com.vgop.service.VgopServiceApplication","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.64+08:00","@version":"1","message":"开始验证应用配置...","logger_name":"com.vgop.service.service.ConfigValidationService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.641+08:00","@version":"1","message":"配置验证通过","logger_name":"com.vgop.service.service.ConfigValidationService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.641+08:00","@version":"1","message":"开始初始化目录结构...","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.642+08:00","@version":"1","message":"目录已存在: 数据导出根目录 - ./VGOPdata/datafile/","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.642+08:00","@version":"1","message":"目录已存在: 日志根目录 - ./logs/","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.643+08:00","@version":"1","message":"目录已存在: 备份根目录 - ./data/backup/","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.643+08:00","@version":"1","message":"目录已存在: 告警文件目录 - ./data/alerts/","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.643+08:00","@version":"1","message":"目录已存在: 日数据目录 - 20250718 - ./VGOPdata/datafile//20250718/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.644+08:00","@version":"1","message":"目录已存在: 月数据目录 - 20250718 - ./VGOPdata/datafile//20250718/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.644+08:00","@version":"1","message":"目录已存在: 日数据目录 - 20250719 - ./VGOPdata/datafile//20250719/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.645+08:00","@version":"1","message":"目录已存在: 月数据目录 - 20250719 - ./VGOPdata/datafile//20250719/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.645+08:00","@version":"1","message":"目录已存在: 日数据目录 - 20250720 - ./VGOPdata/datafile//20250720/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.645+08:00","@version":"1","message":"目录已存在: 月数据目录 - 20250720 - ./VGOPdata/datafile//20250720/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.645+08:00","@version":"1","message":"目录已存在: 日数据目录 - 20250721 - ./VGOPdata/datafile//20250721/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.646+08:00","@version":"1","message":"目录已存在: 月数据目录 - 20250721 - ./VGOPdata/datafile//20250721/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.646+08:00","@version":"1","message":"目录已存在: 日数据目录 - 20250722 - ./VGOPdata/datafile//20250722/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.647+08:00","@version":"1","message":"目录已存在: 月数据目录 - 20250722 - ./VGOPdata/datafile//20250722/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.647+08:00","@version":"1","message":"目录已存在: 日数据目录 - 20250723 - ./VGOPdata/datafile//20250723/day","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.647+08:00","@version":"1","message":"目录已存在: 月数据目录 - 20250723 - ./VGOPdata/datafile//20250723/month","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.647+08:00","@version":"1","message":"目录已存在: 月度数据目录 - 202507 - ./VGOPdata/datafile//202507","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.648+08:00","@version":"1","message":"目录结构初始化完成","logger_name":"com.vgop.service.service.DirectoryInitService","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.65+08:00","@version":"1","message":"开始从配置文件加载校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.65+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24201 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.651+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.652+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.652+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.653+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.653+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.653+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.655+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phonestate.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.655+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonestate, 规则ID=common.phonestate.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.655+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phonestate 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phoneimsi.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimsi, 规则ID=common.phoneimsi.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phoneimsi 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.phoneimei.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimei, 规则ID=common.phoneimei.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 phoneimei 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.locationid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=locationid, 规则ID=common.locationid.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 locationid 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.656+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.provinceid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=provinceid, 规则ID=common.provinceid.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 provinceid 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.openingtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 openingtime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.optime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 Optime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24201.sex.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24201, 字段=sex, 规则ID=common.sex.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.657+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 字段 sex 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.658+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24201, 字段数量=9","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.658+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 注册了 9 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.658+08:00","@version":"1","message":"注册字段到接口映射: [phonestate, Optime, phoneimei, openingtime, locationid, sex, phonenumber, phoneimsi, provinceid] -> VGOP1-R2-10-24201","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.659+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24201 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.659+08:00","@version":"1","message":"接口 VGOP1-R2-10-24201 加载了 10 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.659+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24202 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.659+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.659+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.659+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.659+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.659+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.66+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.66+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.66+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.66+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.66+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.66+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.66+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.66+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.business.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.66+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=business, 规则ID=common.business.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 business 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.shutdown.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 shutdown 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.661+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnlocationid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.662+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnlocationid, 规则ID=common.mcnlocationid.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.662+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnlocationid 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.662+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.numstate.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.662+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=numstate, 规则ID=common.numstate.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.662+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 numstate 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.662+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnature.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.662+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnature, 规则ID=common.mcnnature.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.662+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnature 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.662+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnum.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcnnum.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.channel.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=channel, 规则ID=common.channel.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 channel 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mj.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mj, 规则ID=common.mj.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mj 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.openingtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.664+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 openingtime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.optime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 Optime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.mcimsitime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcimsitime, 规则ID=common.mcimsitime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 mcimsitime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.usertype.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=usertype, 规则ID=common.usertype.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 usertype 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.begintime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.665+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Begintime, 规则ID=common.Begintime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 Begintime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.endtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Endtime, 规则ID=common.Endtime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 Endtime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24202.servid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24202, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 字段 ServID 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24202, 字段数量=18","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 注册了 18 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.666+08:00","@version":"1","message":"注册字段到接口映射: [mcnlocationid, Optime, business, mcnimsi, mcimsitime, phonenumber, mcnnum, channel, usertype, numstate, Endtime, openingtime, ServID, mcnnature, Begintime, mcnnumber, mj, shutdown] -> VGOP1-R2-10-24202","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24202 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"接口 VGOP1-R2-10-24202 加载了 22 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24203 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.account_id.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=account_id, 规则ID=common.account_id.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 account_id 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.667+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.668+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.type.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.668+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=type, 规则ID=common.type.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.668+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 type 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.668+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.optime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.668+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.668+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 optime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.668+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24203.version.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.668+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24203, 字段=version, 规则ID=common.version.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.668+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 字段 version 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.669+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24203, 字段数量=5","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.669+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 注册了 5 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.669+08:00","@version":"1","message":"注册字段到接口映射: [account_id, optime, phonenumber, type, version] -> VGOP1-R2-10-24203","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.669+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24203 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"接口 VGOP1-R2-10-24203 加载了 6 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24205 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.calltype.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callType, 规则ID=common.callType.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 callType 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.67+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.mcnnumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 mcnnumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callbegintime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallBeginTime, 规则ID=common.CallBeginTime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 CallBeginTime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callendtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallEndTime, 规则ID=common.CallEndTime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 CallEndTime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.671+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callduration.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.672+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.672+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.672+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24205.callduration.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.672+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.672+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.672+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24205, 字段数量=7","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 注册了 7 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"注册字段到接口映射: [CallEndTime, callingPartyNumber, calledPartyNumber, CallBeginTime, mcnnumber, CallDuration, callType] -> VGOP1-R2-10-24205","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24205 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"接口 VGOP1-R2-10-24205 加载了 10 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24206 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.chargetype.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=chargetype, 规则ID=common.chargetype.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 chargetype 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.673+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.mcnnumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 mcnnumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.674+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24206.optime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24206, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 字段 optime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24206, 字段数量=5","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 注册了 5 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"注册字段到接口映射: [optime, phonenumber, chargetype, mcnnumber, sendorreceNum] -> VGOP1-R2-10-24206","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24206 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"接口 VGOP1-R2-10-24206 加载了 7 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.675+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-10-24207 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.676+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.phonenumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.676+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.676+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.676+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.phonenumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.676+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.676+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.676+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.676+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.676+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.businessstate.enum - ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=businessState, 规则ID=common.businessState.enum, 规则名称=枚举值校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.677+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 businessState 添加了规则: ENUM","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.numstate.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Numstate, 规则ID=common.Numstate.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 Numstate 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.locationid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Locationid, 规则ID=common.Locationid.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 Locationid 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.10-24207.openingtime.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-10-24207, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 字段 openingtime 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-10-24207, 字段数量=8","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.678+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 注册了 8 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"注册字段到接口映射: [Numstate, openingtime, mcnImsi, businessState, BossProvinceid, phonenumber, Locationid, mcnnumber] -> VGOP1-R2-10-24207","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"为接口 VGOP1-R2-10-24207 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"接口 VGOP1-R2-10-24207 加载了 12 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-11-24101 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.phonenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.phonenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.mcnnum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.679+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.mcnnum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.appactivenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.appactivenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.paynum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.68+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.paynum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.feenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.feenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.secphonenum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.secphonenum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.681+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.amcnnum.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.11-24101.amcnnum.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-11-24101, 字段数量=9","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.682+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 注册了 9 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"注册字段到接口映射: [appactivenum, paynum, mcnnum, secphonenum, phonenum, mcnactivenum, secmcnnum, amcnnum, feenum] -> VGOP1-R2-11-24101","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"为接口 VGOP1-R2-11-24101 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"接口 VGOP1-R2-11-24101 加载了 18 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-13-24301 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servid.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servid.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servid.standard_format - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.683+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servname.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24301.servname.content - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-13-24301, 字段数量=2","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 注册了 2 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"注册字段到接口映射: [ServName, ServID] -> VGOP1-R2-13-24301","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24301 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"接口 VGOP1-R2-13-24301 加载了 5 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-13-24302 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelcode.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelcode.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.684+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelcode.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelname.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24302.channelname.content - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-13-24302, 字段数量=2","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.685+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 注册了 2 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"注册字段到接口映射: [channelname, channelcode] -> VGOP1-R2-13-24302","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24302 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"接口 VGOP1-R2-13-24302 加载了 5 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"正在加载接口 VGOP1-R2-13-24303 的校验规则...","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdown.format - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdown.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.686+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdown.range - RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.range, 规则名称=范围校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: RANGE","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdownname.length - LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.length, 规则名称=长度校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: LENGTH","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"成功创建规则实例: VGOP1-R2.13-24303.shutdownname.content - FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.format, 规则名称=格式校验","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: FORMAT","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"注册字段元数据: 接口=VGOP1-R2-13-24303, 字段数量=2","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 注册了 2 个字段的元数据","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"注册字段到接口映射: [shutdownname, shutdown] -> VGOP1-R2-13-24303","logger_name":"com.vgop.service.validation.ValidationEngine","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"为接口 VGOP1-R2-13-24303 注册了字段到接口的映射关系","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.687+08:00","@version":"1","message":"接口 VGOP1-R2-13-24303 加载了 5 个字段校验规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:41:52.688+08:00","@version":"1","message":"校验规则加载完成，共加载 100 个规则","logger_name":"com.vgop.service.config.ValidationRuleConfig","thread_name":"restartedMain","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:05.14+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/vgop]","thread_name":"http-nio-8080-exec-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:05.14+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8080-exec-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:05.142+08:00","@version":"1","message":"Completed initialization in 2 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8080-exec-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:05.369+08:00","@version":"1","message":"接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.10-24205","logger_name":"com.vgop.service.controller.VgopTaskController","thread_name":"http-nio-8080-exec-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:08.284+08:00","@version":"1","message":"开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.10-24205","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:22.603+08:00","@version":"1","message":"开始执行数据导出任务: VGOP1-R2.10-24205","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:28.904+08:00","@version":"1","message":"开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20220214, revision=1","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:28.914+08:00","@version":"1","message":"日统计任务导出目录使用前一天日期: 20220214 -> 20220213","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:28.916+08:00","@version":"1","message":"构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:28.917+08:00","@version":"1","message":"临时文件路径: ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-1","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:28.919+08:00","@version":"1","message":"执行UNLOAD命令通过dbaccess: set lock mode to wait 10;\nunload to ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl delimiter '|' select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:28.92+08:00","@version":"1","message":"从URL中提取的数据库名称: bms","logger_name":"com.vgop.service.util.DatabaseUtil","thread_name":"vgop-task-1","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:28.922+08:00","@version":"1","message":"启动dbaccess进程，数据库: bms","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-1","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:28.934+08:00","@version":"1","message":"执行dbaccess命令时发生异常","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-1","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: Cannot run program \"dbaccess\": CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)\r\n\tat com.vgop.service.service.DataExportService.exportData(DataExportService.java:153)\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\nCaused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessImpl.create(Native Method)\r\n\tat java.lang.ProcessImpl.<init>(ProcessImpl.java:459)\r\n\tat java.lang.ProcessImpl.start(ProcessImpl.java:139)\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)\r\n\t... 16 common frames omitted\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:28.936+08:00","@version":"1","message":"dbaccess命令执行失败","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-1","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-1","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:42.428+08:00","@version":"1","message":"统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.10-24205, 错误: 数据导出失败: UNLOAD执行失败","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-1","level":"ERROR","level_value":40000,"stack_trace":"com.vgop.service.exception.TaskExecutionException: 数据导出失败: UNLOAD执行失败\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:49.48+08:00","@version":"1","message":"接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.10-24205","logger_name":"com.vgop.service.controller.VgopTaskController","thread_name":"http-nio-8080-exec-1","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:51.12+08:00","@version":"1","message":"开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.10-24205","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:51.12+08:00","@version":"1","message":"开始执行数据导出任务: VGOP1-R2.10-24205","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:42:53.293+08:00","@version":"1","message":"开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20220214, revision=1","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:44:33.544+08:00","@version":"1","message":"日统计任务导出目录使用前一天日期: 20220214 -> 20220213","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:44:55.336+08:00","@version":"1","message":"构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:27.722+08:00","@version":"1","message":"临时文件路径: ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-2","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:42.872+08:00","@version":"1","message":"执行UNLOAD命令通过dbaccess: set lock mode to wait 10;\nunload to ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl delimiter '|' select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-2","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:42.882+08:00","@version":"1","message":"从URL中提取的数据库名称: bms","logger_name":"com.vgop.service.util.DatabaseUtil","thread_name":"vgop-task-2","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:42.882+08:00","@version":"1","message":"启动dbaccess进程，数据库: bms","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-2","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:42.886+08:00","@version":"1","message":"执行dbaccess命令时发生异常","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-2","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: Cannot run program \"dbaccess\": CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)\r\n\tat com.vgop.service.service.DataExportService.exportData(DataExportService.java:153)\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\nCaused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessImpl.create(Native Method)\r\n\tat java.lang.ProcessImpl.<init>(ProcessImpl.java:459)\r\n\tat java.lang.ProcessImpl.start(ProcessImpl.java:139)\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)\r\n\t... 16 common frames omitted\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:42.886+08:00","@version":"1","message":"dbaccess命令执行失败","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-2","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-2","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:42.886+08:00","@version":"1","message":"统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.10-24205, 错误: 数据导出失败: UNLOAD执行失败","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-2","level":"ERROR","level_value":40000,"stack_trace":"com.vgop.service.exception.TaskExecutionException: 数据导出失败: UNLOAD执行失败\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:49.656+08:00","@version":"1","message":"接收到任务执行请求: manual-exec-001, 接口ID: VGOP1-R2.10-24205","logger_name":"com.vgop.service.controller.VgopTaskController","thread_name":"http-nio-8080-exec-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:50.839+08:00","@version":"1","message":"开始执行统计任务: manual-exec-001, 接口ID: VGOP1-R2.10-24205","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:50.84+08:00","@version":"1","message":"开始执行数据导出任务: VGOP1-R2.10-24205","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:54.007+08:00","@version":"1","message":"开始导出接口数据: interfaceId=VGOP1-R2.10-24205, dataDate=20220214, revision=1","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:54.008+08:00","@version":"1","message":"日统计任务导出目录使用前一天日期: 20220214 -> 20220213","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:54.008+08:00","@version":"1","message":"构建UNLOAD SQL: select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:45:54.008+08:00","@version":"1","message":"临时文件路径: ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl","logger_name":"com.vgop.service.service.DataExportService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","dataDate":"20220214","operationType":"export","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:46:09.81+08:00","@version":"1","message":"执行UNLOAD命令通过dbaccess: set lock mode to wait 10;\nunload to ./VGOPdata/datafile/20220213/daily/a_10000_20220213_VGOP1-R2.10-24205.unl delimiter '|' select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='20220213000000' and CallBeginTime<'20220214000000' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"INFO","level_value":20000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:46:28.72+08:00","@version":"1","message":"从URL中提取的数据库名称: bms","logger_name":"com.vgop.service.util.DatabaseUtil","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:46:28.72+08:00","@version":"1","message":"启动dbaccess进程，数据库: bms","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:46:28.722+08:00","@version":"1","message":"执行dbaccess命令时发生异常","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: Cannot run program \"dbaccess\": CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)\r\n\tat com.vgop.service.service.DataExportService.exportData(DataExportService.java:153)\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\nCaused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessImpl.create(Native Method)\r\n\tat java.lang.ProcessImpl.<init>(ProcessImpl.java:459)\r\n\tat java.lang.ProcessImpl.start(ProcessImpl.java:139)\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)\r\n\t... 16 common frames omitted\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:46:39.154+08:00","@version":"1","message":"从URL中提取的数据库名称: bms","logger_name":"com.vgop.service.util.DatabaseUtil","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:46:39.154+08:00","@version":"1","message":"启动dbaccess进程，数据库: bms","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"DEBUG","level_value":10000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:46:39.159+08:00","@version":"1","message":"执行dbaccess命令时发生异常","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"ERROR","level_value":40000,"stack_trace":"java.io.IOException: Cannot run program \"dbaccess\": CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeDbAccessCommand(UnloadExecutorService.java:189)\r\n\tat com.vgop.service.service.UnloadExecutorService.executeUnload(UnloadExecutorService.java:78)\r\n\tat com.vgop.service.service.DataExportService.exportData(DataExportService.java:153)\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:74)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\nCaused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。\r\n\tat java.lang.ProcessImpl.create(Native Method)\r\n\tat java.lang.ProcessImpl.<init>(ProcessImpl.java:459)\r\n\tat java.lang.ProcessImpl.start(ProcessImpl.java:139)\r\n\tat java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)\r\n\t... 16 common frames omitted\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:46:39.159+08:00","@version":"1","message":"dbaccess命令执行失败","logger_name":"com.vgop.service.service.UnloadExecutorService","thread_name":"vgop-task-3","level":"ERROR","level_value":40000,"APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","operationType":"unload","dataDate":"20220214","interfaceId":"VGOP1-R2.10-24205","threadName":"vgop-task-3","revision":"1","application":"vgop-service"}
{"@timestamp":"2025-07-21T13:46:39.16+08:00","@version":"1","message":"统计任务 manual-exec-001 执行失败, 接口ID: VGOP1-R2.10-24205, 错误: 数据导出失败: UNLOAD执行失败","logger_name":"com.vgop.service.service.VgopTaskScheduler","thread_name":"vgop-task-3","level":"ERROR","level_value":40000,"stack_trace":"com.vgop.service.exception.TaskExecutionException: 数据导出失败: UNLOAD执行失败\r\n\tat com.vgop.service.service.VgopTaskScheduler.executeTask(VgopTaskScheduler.java:76)\r\n\tat com.vgop.service.service.VgopTaskScheduler$$FastClassBySpringCGLIB$$62377ee3.invoke(<generated>)\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)\r\n\tat org.springframework.aop.interceptor.AsyncExecutionAspectSupport.lambda$doSubmit$3(AsyncExecutionAspectSupport.java:276)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)\r\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\n","APP_NAME":"vgop-service","LOG_ROOT":"./logs/","LOG_LEVEL":"DEBUG","application":"vgop-service"}
