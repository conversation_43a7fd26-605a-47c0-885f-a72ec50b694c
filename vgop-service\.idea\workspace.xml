<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="72bd0a04-d42d-40a5-9237-44fc7aac0b16" name="Changes" comment="feat: 数据源调整">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/DataExportService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/DataExportService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/VgopTaskScheduler.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/VgopTaskScheduler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="C:\Users\<USER>\.m2\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2yLqfOgQMKiFmeCQXpf42qjKnFq" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.FileValidator.executor&quot;: &quot;Debug&quot;,
    &quot;Application.VgopServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Maven.vgop-service [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.vgop-service [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.vgop-service [package].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.VgopServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/workspaces/zjh/vgop/vgop-service/VGOPdata/datafile/20250622/day&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDKs&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\workspaces\zjh\vgop\vgop-service\VGOPdata\datafile\20250622\day" />
      <recent name="C:\workspaces\zjh\vgop\vgop-service\VGOPdata\datafile\20250620\day" />
      <recent name="C:\workspaces\zjh\vgop\vgop-service\VGOPdata\datafile\20250618\day" />
      <recent name="C:\workspaces\zjh\vgop\vgop-service\VGOPdata\datafile\20250617\day" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.VgopServiceApplication">
    <configuration name="FileValidator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.vgop.service.validation.FileValidator" />
      <module name="vgop-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.vgop.service.validation.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="VgopServiceApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.vgop.service.VgopServiceApplication" />
      <module name="vgop-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.vgop.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="VgopServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="vgop-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.vgop.service.VgopServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.FileValidator" />
        <item itemvalue="Application.VgopServiceApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="72bd0a04-d42d-40a5-9237-44fc7aac0b16" name="Changes" comment="" />
      <created>1749624303978</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749624303978</updated>
      <workItem from="1749624305173" duration="3881000" />
      <workItem from="1749709425498" duration="1486000" />
      <workItem from="1749710925321" duration="11311000" />
      <workItem from="1749780857640" duration="13293000" />
      <workItem from="1750041151551" duration="1191000" />
      <workItem from="1750127692282" duration="2222000" />
      <workItem from="1750153051440" duration="1517000" />
      <workItem from="1750212280804" duration="15477000" />
      <workItem from="1750297867367" duration="7751000" />
      <workItem from="1750316296818" duration="5115000" />
      <workItem from="1750400028659" duration="10941000" />
      <workItem from="1750591270603" duration="1908000" />
      <workItem from="1750643830252" duration="6345000" />
      <workItem from="1750687920628" duration="3710000" />
      <workItem from="1750729700998" duration="7223000" />
      <workItem from="1750822375241" duration="5390000" />
      <workItem from="1750902171148" duration="2831000" />
      <workItem from="1750944167790" duration="4904000" />
      <workItem from="1750988637052" duration="14070000" />
      <workItem from="1751552384642" duration="5957000" />
      <workItem from="1751614075553" duration="1022000" />
      <workItem from="1751875339525" duration="808000" />
      <workItem from="1752181424849" duration="10000" />
      <workItem from="1752181614678" duration="8000" />
      <workItem from="1752584690422" duration="1333000" />
      <workItem from="1752757851915" duration="3009000" />
      <workItem from="1752779828896" duration="59000" />
      <workItem from="1753067147963" duration="2492000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 数据源调整">
      <option name="closed" value="true" />
      <created>1749720421771</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749720421771</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: 数据源调整" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 数据源调整" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/vgop/service/service/FileTransferService.java</url>
          <line>173</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/vgop/service/validation/ValidationServiceImpl.java</url>
          <line>78</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/vgop/service/service/VgopTaskScheduler.java</url>
          <line>47</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/vgop/service/service/DataExportService.java</url>
          <line>50</line>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>